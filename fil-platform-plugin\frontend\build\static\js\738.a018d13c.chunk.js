"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[738],{1719:(e,s,a)=>{a.d(s,{A:()=>y});var t=a(8139),r=a.n(t),i=a(5043),n=a(1969),l=a(6618),d=a(7852),o=a(4488),c=a(579);const m=(0,o.A)("h4");m.displayName="DivStyledAsH4";const u=i.forwardRef((e,s)=>{let{className:a,bsPrefix:t,as:i=m,...n}=e;return t=(0,d.oU)(t,"alert-heading"),(0,c.jsx)(i,{ref:s,className:r()(a,t),...n})});u.displayName="AlertHeading";const p=u;var f=a(7071);const _=i.forwardRef((e,s)=>{let{className:a,bsPrefix:t,as:i=f.A,...n}=e;return t=(0,d.oU)(t,"alert-link"),(0,c.jsx)(i,{ref:s,className:r()(a,t),...n})});_.displayName="AlertLink";const g=_;var b=a(8072),x=a(5632);const h=i.forwardRef((e,s)=>{const{bsPrefix:a,show:t=!0,closeLabel:i="Close alert",closeVariant:o,className:m,children:u,variant:p="primary",onClose:f,dismissible:_,transition:g=b.A,...h}=(0,n.Zw)(e,{show:"onClose"}),y=(0,d.oU)(a,"alert"),j=(0,l.A)(e=>{f&&f(!1,e)}),v=!0===g?b.A:g,A=(0,c.jsxs)("div",{role:"alert",...v?void 0:h,ref:s,className:r()(m,y,p&&`${y}-${p}`,_&&`${y}-dismissible`),children:[_&&(0,c.jsx)(x.A,{onClick:j,"aria-label":i,variant:o}),u]});return v?(0,c.jsx)(v,{unmountOnExit:!0,...h,ref:void 0,in:t,children:A}):t?A:null});h.displayName="Alert";const y=Object.assign(h,{Link:g,Heading:p})},2738:(e,s,a)=>{a.r(s),a.d(s,{default:()=>u});var t=a(5043),r=a(3519),i=a(8628),n=a(1719),l=a(3722),d=a(4282),o=a(4312),c=a(4117),m=a(579);const u=()=>{const{t:e}=(0,c.Bd)(),[s,a]=(0,t.useState)(""),[u,p]=(0,t.useState)(""),[f,_]=(0,t.useState)(null),[g,b]=(0,t.useState)(null),[x,h]=(0,t.useState)(null),[y,j]=(0,t.useState)(!0),[v,A]=(0,t.useState)(!1),[w,k]=(0,t.useState)({type:"",text:""});(0,t.useEffect)(()=>{(async()=>{const s=(0,o.b)();if(!s)return;const{data:{user:t}}=await s.auth.getUser();if(!t)return void j(!1);const{data:r,error:i}=await s.from("customer_profiles").select("real_name, id_number, id_img_front, id_img_back, verify_status").eq("user_id",t.id).single();i&&"PGRST116"!==i.code?(console.error("Error fetching KYC status:",i),k({type:"danger",text:e("failed_to_load_kyc_status")})):r&&(a(r.real_name||""),p(r.id_number||""),_(r.id_img_front||null),b(r.id_img_back||null),h(r.verify_status||null)),j(!1)})()},[]);const N=(e,s)=>{e.target.files&&e.target.files[0]&&s(e.target.files[0])};return y?(0,m.jsx)("div",{children:e("loading_kyc_status")}):(0,m.jsxs)(r.A,{children:[(0,m.jsx)("h2",{className:"mb-4",children:e("kyc_verification")}),(0,m.jsx)(i.A,{children:(0,m.jsxs)(i.A.Body,{children:[w.text&&(0,m.jsx)(n.A,{variant:w.type,children:w.text}),"approved"===x&&(0,m.jsx)(n.A,{variant:"success",children:e("kyc_approved")}),"pending"===x&&(0,m.jsx)(n.A,{variant:"info",children:e("kyc_pending_review")}),"rejected"===x&&(0,m.jsx)(n.A,{variant:"danger",children:e("kyc_rejected")}),null===x&&(0,m.jsx)(n.A,{variant:"warning",children:e("kyc_not_submitted")}),(0,m.jsxs)(l.A,{onSubmit:async a=>{a.preventDefault(),A(!0),k({type:"",text:""});try{const a=new FormData;a.append("real_name",s),a.append("id_number",u),f instanceof File&&a.append("id_img_front",f),g instanceof File&&a.append("id_img_back",g);const t=await fetch(`${window.wpData.apiUrl}submit-kyc`,{method:"POST",body:a,headers:{"X-WP-Nonce":window.wpData.nonce}}),r=await t.json();if(!r.success)throw new Error(r.message||"Failed to submit KYC");r.files&&r.files.id_img_front&&_(r.files.id_img_front.url),r.files&&r.files.id_img_back&&b(r.files.id_img_back.url),k({type:"success",text:e("kyc_submit_success")}),h("pending"),r.errors&&r.errors.length>0&&console.warn("Upload warnings:",r.errors)}catch(t){console.error("KYC submission error:",t),k({type:"danger",text:e("failed_to_submit_kyc")+": "+t.message})}A(!1)},children:[(0,m.jsxs)(l.A.Group,{className:"mb-3",children:[(0,m.jsx)(l.A.Label,{children:e("real_name")}),(0,m.jsx)(l.A.Control,{type:"text",value:s,onChange:e=>a(e.target.value),required:!0,disabled:"pending"===x||"approved"===x})]}),(0,m.jsxs)(l.A.Group,{className:"mb-3",children:[(0,m.jsx)(l.A.Label,{children:e("id_number")}),(0,m.jsx)(l.A.Control,{type:"text",value:u,onChange:e=>p(e.target.value),required:!0,disabled:"pending"===x||"approved"===x})]}),(0,m.jsxs)(l.A.Group,{className:"mb-3",children:[(0,m.jsx)(l.A.Label,{children:e("id_front")}),(0,m.jsx)(l.A.Control,{type:"file",onChange:e=>N(e,_),accept:"image/*",disabled:"pending"===x||"approved"===x}),f&&"string"===typeof f&&(0,m.jsx)("img",{src:f,alt:"ID Front",className:"img-thumbnail mt-2",style:{maxWidth:"200px"}})]}),(0,m.jsxs)(l.A.Group,{className:"mb-3",children:[(0,m.jsx)(l.A.Label,{children:e("id_back")}),(0,m.jsx)(l.A.Control,{type:"file",onChange:e=>N(e,b),accept:"image/*",disabled:"pending"===x||"approved"===x}),g&&"string"===typeof g&&(0,m.jsx)("img",{src:g,alt:"ID Back",className:"img-thumbnail mt-2",style:{maxWidth:"200px"}})]}),(0,m.jsx)(d.A,{variant:"primary",type:"submit",disabled:v||"pending"===x||"approved"===x,children:e(v?"submitting":"submit_review")})]})]})})]})}}}]);
//# sourceMappingURL=738.a018d13c.chunk.js.map