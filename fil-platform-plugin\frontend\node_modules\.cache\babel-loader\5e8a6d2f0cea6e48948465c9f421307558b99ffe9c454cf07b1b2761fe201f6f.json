{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{Container,Row,Col,Card,Table,Spinner,<PERSON><PERSON>,<PERSON><PERSON>}from'react-bootstrap';import{<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>Axis,<PERSON><PERSON><PERSON><PERSON>,Cartes<PERSON>Grid,<PERSON><PERSON><PERSON>,Legend,ResponsiveContainer}from'recharts';import{getSupabase}from'../../supabaseClient';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const StatCard=_ref=>{let{title,value,unit,loading,icon}=_ref;return/*#__PURE__*/_jsx(Card,{className:`mb-3 h-100`,children:/*#__PURE__*/_jsx(Card.Body,{className:\"d-flex flex-column justify-content-between\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-start\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Card.Title,{className:\"h6\",children:title}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Loading...\"})]}):/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0\",children:value}),unit&&/*#__PURE__*/_jsx(\"small\",{className:\"opacity-75\",children:unit})]})]}),icon&&/*#__PURE__*/_jsx(\"div\",{className:\"fs-2 opacity-50\",children:icon})]})})});};const Filfox=()=>{const{t}=useTranslation();const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[currentStats,setCurrentStats]=useState(null);const[historicalData,setHistoricalData]=useState([]);const[refreshing,setRefreshing]=useState(false);// Fetch real-time network stats from WordPress API\nconst fetchNetworkStats=async()=>{try{setLoading(true);// First test the API connection\nconst testUrl=window.location.origin+'/wp-json/fil-platform/v1/test';console.log('Testing API connection:',testUrl);const testResponse=await fetch(testUrl,{method:'GET',credentials:'include',headers:{'Content-Type':'application/json'}});console.log('Test response status:',testResponse.status);if(testResponse.ok){const testResult=await testResponse.json();console.log('Test API Response:',testResult);}else{console.error('Test API failed:',testResponse.status);}// Now try the real endpoint\nconst wpApiUrl=window.location.origin+'/wp-json/fil-platform/v1/filfox-realtime';console.log('Fetching from URL:',wpApiUrl);const response=await fetch(wpApiUrl,{method:'GET',credentials:'include',// Include cookies for authentication\nheaders:{'Content-Type':'application/json'}});console.log('Response status:',response.status);if(!response.ok){const errorText=await response.text();console.error('Error response:',errorText);throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);}const result=await response.json();console.log('API Response:',result);if(result.success){setCurrentStats(result.data);setError(null);}else{setError(result.message||'Failed to fetch real-time data');}// For historical data, we'll fetch from Supabase (only fil_per_tib)\nawait fetchHistoricalData();}catch(error){console.error('Error fetching real-time stats:',error);setError('Failed to load real-time network statistics: '+error.message);}finally{setLoading(false);setRefreshing(false);}};// Fetch historical data for charts (only fil_per_tib from database)\nconst fetchHistoricalData=async()=>{const supabase=getSupabase();if(!supabase)return;try{const{data:{user}}=await supabase.auth.getUser();if(!user)return;// Fetch historical data for charts (last 30 days, only fil_per_tib)\nconst{data:historicalData,error:historicalError}=await supabase.from('network_stats').select('stat_date, fil_per_tib').order('stat_date',{ascending:false}).limit(30);if(historicalError){console.error('Error fetching historical stats:',historicalError);}else{// Reverse to show chronological order in charts\nsetHistoricalData(historicalData.reverse());}}catch(error){console.error('Error fetching historical data:',error);}};useEffect(()=>{fetchNetworkStats();},[]);const handleRefresh=()=>{setRefreshing(true);fetchNetworkStats();};const formatNumber=num=>{if(num===null||num===undefined)return'N/A';return new Intl.NumberFormat('en-US',{minimumFractionDigits:0,maximumFractionDigits:4}).format(num);};const formatDate=dateString=>{return new Date(dateString).toLocaleDateString();};if(error){return/*#__PURE__*/_jsx(Container,{fluid:true,children:/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(\"h2\",{children:t('filfox_network_stats')}),/*#__PURE__*/_jsx(Alert,{children:error})]})})});}return/*#__PURE__*/_jsxs(Container,{fluid:true,children:[/*#__PURE__*/_jsx(Row,{className:\"mb-3\",children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex justify-content-between align-items-center\",children:[/*#__PURE__*/_jsx(\"h2\",{children:t('filfox_network_stats')}),/*#__PURE__*/_jsx(Button,{onClick:handleRefresh,disabled:refreshing,children:refreshing?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\",size:\"sm\",className:\"me-2\"}),t('refreshing')]}):t('refresh')})]})})}),/*#__PURE__*/_jsxs(Row,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('block_height'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.block_height),loading:loading,icon:\"\\uD83D\\uDD17\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('network_storage_power'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.network_storage_power),unit:\"EiB\",loading:loading,icon:\"\\uD83D\\uDCBE\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('active_miners'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.active_miners),loading:loading,icon:\"\\u26CF\\uFE0F\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('block_reward'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.block_reward),unit:\"FIL\",loading:loading,icon:\"\\uD83C\\uDF81\"})})]}),/*#__PURE__*/_jsxs(Row,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('mining_reward_24h'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.mining_reward),unit:\"FIL/TiB\",loading:loading,icon:\"\\u26A1\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('fil_production_24h'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.fil_production_24h),unit:\"FIL\",loading:loading,icon:\"\\uD83C\\uDFED\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('total_pledge_collateral'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.total_pledge_collateral),unit:\"FIL\",loading:loading,icon:\"\\uD83D\\uDD12\"})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(StatCard,{title:t('messages_24h'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.messages_24h),loading:loading,icon:\"\\uD83D\\uDCE8\"})})]}),/*#__PURE__*/_jsxs(Row,{className:\"mb-4\",children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsx(StatCard,{title:t('sector_initial_pledge'),value:formatNumber(currentStats===null||currentStats===void 0?void 0:currentStats.sector_initial_pledge),unit:\"FIL/32GiB\",loading:loading,icon:\"\\uD83D\\uDD10\"})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsx(StatCard,{title:t('latest_block'),value:(currentStats===null||currentStats===void 0?void 0:currentStats.latest_block)||'N/A',loading:loading,icon:\"\\u23F0\"})})]}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsx(Card.Title,{children:t('current_network_summary')}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(Spinner,{animation:\"border\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2\",children:t('loading')})]}):currentStats?/*#__PURE__*/_jsx(Table,{striped:true,bordered:true,hover:true,responsive:true,children:/*#__PURE__*/_jsxs(\"tbody\",{children:[/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('block_height')})}),/*#__PURE__*/_jsx(\"td\",{children:formatNumber(currentStats.block_height)}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('network_storage_power')})}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(currentStats.network_storage_power),\" EiB\"]})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('active_miners')})}),/*#__PURE__*/_jsx(\"td\",{children:formatNumber(currentStats.active_miners)}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('block_reward')})}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(currentStats.block_reward),\" FIL\"]})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('mining_reward_24h')})}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(currentStats.mining_reward),\" FIL/TiB\"]}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('fil_production_24h')})}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(currentStats.fil_production_24h),\" FIL\"]})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('total_pledge_collateral')})}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(currentStats.total_pledge_collateral),\" FIL\"]}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('messages_24h')})}),/*#__PURE__*/_jsx(\"td\",{children:formatNumber(currentStats.messages_24h)})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('sector_initial_pledge')})}),/*#__PURE__*/_jsxs(\"td\",{children:[formatNumber(currentStats.sector_initial_pledge),\" FIL/32GiB\"]}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('latest_block')})}),/*#__PURE__*/_jsx(\"td\",{children:currentStats.latest_block||'N/A'})]}),/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"strong\",{children:t('last_updated')})}),/*#__PURE__*/_jsx(\"td\",{colSpan:\"3\",children:currentStats.scraped_at?new Date(currentStats.scraped_at).toLocaleString():'N/A'})]})]})}):/*#__PURE__*/_jsx(\"p\",{children:t('no_data_available')})]})})})})]});};export default Filfox;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "Container", "Row", "Col", "Card", "Table", "Spinner", "<PERSON><PERSON>", "<PERSON><PERSON>", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "getSupabase", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "StatCard", "_ref", "title", "value", "unit", "loading", "icon", "className", "children", "Body", "Title", "animation", "size", "Filfox", "t", "setLoading", "error", "setError", "currentStats", "setCurrentStats", "historicalData", "setHistoricalData", "refreshing", "setRefreshing", "fetchNetworkStats", "testUrl", "window", "location", "origin", "console", "log", "testResponse", "fetch", "method", "credentials", "headers", "status", "ok", "testResult", "json", "wpApiUrl", "response", "errorText", "text", "Error", "result", "success", "data", "message", "fetchHistoricalData", "supabase", "user", "auth", "getUser", "historicalError", "from", "select", "order", "ascending", "limit", "reverse", "handleRefresh", "formatNumber", "num", "undefined", "Intl", "NumberFormat", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDate", "dateString", "Date", "toLocaleDateString", "fluid", "onClick", "disabled", "md", "block_height", "network_storage_power", "active_miners", "block_reward", "mining_reward", "fil_production_24h", "total_pledge_collateral", "messages_24h", "sector_initial_pledge", "latest_block", "striped", "bordered", "hover", "responsive", "colSpan", "scraped_at", "toLocaleString"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/customer/Filfox.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Container, Row, Col, Card, Table, Spinner, Al<PERSON>, Button } from 'react-bootstrap';\nimport { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';\nimport { getSupabase } from '../../supabaseClient';\n\nconst StatCard = ({ title, value, unit, loading, icon }) => (\n    <Card className={`mb-3 h-100`}>\n        <Card.Body className=\"d-flex flex-column justify-content-between\">\n            <div className=\"d-flex justify-content-between align-items-start\">\n                <div>\n                    <Card.Title className=\"h6\">{title}</Card.Title>\n                    {loading ? (\n                        <div className=\"d-flex align-items-center\">\n                            <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                            <span>Loading...</span>\n                        </div>\n                    ) : (\n                        <div>\n                            <h4 className=\"mb-0\">{value}</h4>\n                            {unit && <small className=\"opacity-75\">{unit}</small>}\n                        </div>\n                    )}\n                </div>\n                {icon && <div className=\"fs-2 opacity-50\">{icon}</div>}\n            </div>\n        </Card.Body>\n    </Card>\n);\n\nconst Filfox = () => {\n    const { t } = useTranslation();\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [currentStats, setCurrentStats] = useState(null);\n    const [historicalData, setHistoricalData] = useState([]);\n    const [refreshing, setRefreshing] = useState(false);\n\n    // Fetch real-time network stats from WordPress API\n    const fetchNetworkStats = async () => {\n        try {\n            setLoading(true);\n\n            // First test the API connection\n            const testUrl = window.location.origin + '/wp-json/fil-platform/v1/test';\n            console.log('Testing API connection:', testUrl);\n\n            const testResponse = await fetch(testUrl, {\n                method: 'GET',\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json',\n                }\n            });\n\n            console.log('Test response status:', testResponse.status);\n\n            if (testResponse.ok) {\n                const testResult = await testResponse.json();\n                console.log('Test API Response:', testResult);\n            } else {\n                console.error('Test API failed:', testResponse.status);\n            }\n\n            // Now try the real endpoint\n            const wpApiUrl = window.location.origin + '/wp-json/fil-platform/v1/filfox-realtime';\n            console.log('Fetching from URL:', wpApiUrl);\n\n            const response = await fetch(wpApiUrl, {\n                method: 'GET',\n                credentials: 'include', // Include cookies for authentication\n                headers: {\n                    'Content-Type': 'application/json',\n                }\n            });\n\n            console.log('Response status:', response.status);\n\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Error response:', errorText);\n                throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);\n            }\n\n            const result = await response.json();\n            console.log('API Response:', result);\n\n            if (result.success) {\n                setCurrentStats(result.data);\n                setError(null);\n            } else {\n                setError(result.message || 'Failed to fetch real-time data');\n            }\n\n            // For historical data, we'll fetch from Supabase (only fil_per_tib)\n            await fetchHistoricalData();\n\n        } catch (error) {\n            console.error('Error fetching real-time stats:', error);\n            setError('Failed to load real-time network statistics: ' + error.message);\n        } finally {\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n\n    // Fetch historical data for charts (only fil_per_tib from database)\n    const fetchHistoricalData = async () => {\n        const supabase = getSupabase();\n        if (!supabase) return;\n\n        try {\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) return;\n\n            // Fetch historical data for charts (last 30 days, only fil_per_tib)\n            const { data: historicalData, error: historicalError } = await supabase\n                .from('network_stats')\n                .select('stat_date, fil_per_tib')\n                .order('stat_date', { ascending: false })\n                .limit(30);\n\n            if (historicalError) {\n                console.error('Error fetching historical stats:', historicalError);\n            } else {\n                // Reverse to show chronological order in charts\n                setHistoricalData(historicalData.reverse());\n            }\n        } catch (error) {\n            console.error('Error fetching historical data:', error);\n        }\n    };\n\n    useEffect(() => {\n        fetchNetworkStats();\n    }, []);\n\n    const handleRefresh = () => {\n        setRefreshing(true);\n        fetchNetworkStats();\n    };\n\n    const formatNumber = (num) => {\n        if (num === null || num === undefined) return 'N/A';\n        return new Intl.NumberFormat('en-US', {\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 4\n        }).format(num);\n    };\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString();\n    };\n\n    if (error) {\n        return (\n            <Container fluid>\n                <Row className=\"mb-3\">\n                    <Col>\n                        <h2>{t('filfox_network_stats')}</h2>\n                        <Alert>{error}</Alert>\n                    </Col>\n                </Row>\n            </Container>\n        );\n    }\n\n    return (\n        <Container fluid>\n            <Row className=\"mb-3\">\n                <Col>\n                    <div className=\"d-flex justify-content-between align-items-center\">\n                        <h2>{t('filfox_network_stats')}</h2>\n                        <Button \n                            onClick={handleRefresh}\n                            disabled={refreshing}\n                        >\n                            {refreshing ? (\n                                <>\n                                    <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\n                                    {t('refreshing')}\n                                </>\n                            ) : (\n                                t('refresh')\n                            )}\n                        </Button>\n                    </div>\n                </Col>\n            </Row>\n\n            {/* Current Statistics Cards */}\n            <Row className=\"mb-4\">\n                <Col md={3}>\n                    <StatCard\n                        title={t('block_height')}\n                        value={formatNumber(currentStats?.block_height)}\n                        loading={loading}\n                        icon=\"🔗\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('network_storage_power')}\n                        value={formatNumber(currentStats?.network_storage_power)}\n                        unit=\"EiB\"\n                        loading={loading}\n                        icon=\"💾\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('active_miners')}\n                        value={formatNumber(currentStats?.active_miners)}\n                        loading={loading}\n                        icon=\"⛏️\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('block_reward')}\n                        value={formatNumber(currentStats?.block_reward)}\n                        unit=\"FIL\"\n                        loading={loading}\n                        icon=\"🎁\"\n                    />\n                </Col>\n            </Row>\n\n            <Row className=\"mb-4\">\n                <Col md={3}>\n                    <StatCard\n                        title={t('mining_reward_24h')}\n                        value={formatNumber(currentStats?.mining_reward)}\n                        unit=\"FIL/TiB\"\n                        loading={loading}\n                        icon=\"⚡\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('fil_production_24h')}\n                        value={formatNumber(currentStats?.fil_production_24h)}\n                        unit=\"FIL\"\n                        loading={loading}\n                        icon=\"🏭\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('total_pledge_collateral')}\n                        value={formatNumber(currentStats?.total_pledge_collateral)}\n                        unit=\"FIL\"\n                        loading={loading}\n                        icon=\"🔒\"\n                    />\n                </Col>\n                <Col md={3}>\n                    <StatCard\n                        title={t('messages_24h')}\n                        value={formatNumber(currentStats?.messages_24h)}\n                        loading={loading}\n                        icon=\"📨\"\n                    />\n                </Col>\n            </Row>\n\n            {/* Additional Stats */}\n            <Row className=\"mb-4\">\n                <Col md={6}>\n                    <StatCard\n                        title={t('sector_initial_pledge')}\n                        value={formatNumber(currentStats?.sector_initial_pledge)}\n                        unit=\"FIL/32GiB\"\n                        loading={loading}\n                        icon=\"🔐\"\n                    />\n                </Col>\n                <Col md={6}>\n                    <StatCard\n                        title={t('latest_block')}\n                        value={currentStats?.latest_block || 'N/A'}\n                        loading={loading}\n                        icon=\"⏰\"\n                    />\n                </Col>\n            </Row>\n\n            {/* Current Data Summary */}\n            <Row>\n                <Col>\n                    <Card>\n                        <Card.Body>\n                            <Card.Title>{t('current_network_summary')}</Card.Title>\n                            {loading ? (\n                                <div className=\"text-center\">\n                                    <Spinner animation=\"border\" />\n                                    <p className=\"mt-2\">{t('loading')}</p>\n                                </div>\n                            ) : currentStats ? (\n                                <Table striped bordered hover responsive>\n                                    <tbody>\n                                        <tr>\n                                            <td><strong>{t('block_height')}</strong></td>\n                                            <td>{formatNumber(currentStats.block_height)}</td>\n                                            <td><strong>{t('network_storage_power')}</strong></td>\n                                            <td>{formatNumber(currentStats.network_storage_power)} EiB</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('active_miners')}</strong></td>\n                                            <td>{formatNumber(currentStats.active_miners)}</td>\n                                            <td><strong>{t('block_reward')}</strong></td>\n                                            <td>{formatNumber(currentStats.block_reward)} FIL</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('mining_reward_24h')}</strong></td>\n                                            <td>{formatNumber(currentStats.mining_reward)} FIL/TiB</td>\n                                            <td><strong>{t('fil_production_24h')}</strong></td>\n                                            <td>{formatNumber(currentStats.fil_production_24h)} FIL</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('total_pledge_collateral')}</strong></td>\n                                            <td>{formatNumber(currentStats.total_pledge_collateral)} FIL</td>\n                                            <td><strong>{t('messages_24h')}</strong></td>\n                                            <td>{formatNumber(currentStats.messages_24h)}</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('sector_initial_pledge')}</strong></td>\n                                            <td>{formatNumber(currentStats.sector_initial_pledge)} FIL/32GiB</td>\n                                            <td><strong>{t('latest_block')}</strong></td>\n                                            <td>{currentStats.latest_block || 'N/A'}</td>\n                                        </tr>\n                                        <tr>\n                                            <td><strong>{t('last_updated')}</strong></td>\n                                            <td colSpan=\"3\">{currentStats.scraped_at ? new Date(currentStats.scraped_at).toLocaleString() : 'N/A'}</td>\n                                        </tr>\n                                    </tbody>\n                                </Table>\n                            ) : (\n                                <p>{t('no_data_available')}</p>\n                            )}\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default Filfox;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,KAAK,CAAEC,OAAO,CAAEC,KAAK,CAAEC,MAAM,KAAQ,iBAAiB,CAC1F,OAASC,SAAS,CAAEC,IAAI,CAAEC,KAAK,CAAEC,KAAK,CAAEC,aAAa,CAAEC,OAAO,CAAEC,MAAM,CAAEC,mBAAmB,KAAQ,UAAU,CAC7G,OAASC,WAAW,KAAQ,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEnD,KAAM,CAAAC,QAAQ,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,KAAK,CAAEC,IAAI,CAAEC,OAAO,CAAEC,IAAK,CAAC,CAAAL,IAAA,oBACnDN,IAAA,CAACf,IAAI,EAAC2B,SAAS,CAAE,YAAa,CAAAC,QAAA,cAC1Bb,IAAA,CAACf,IAAI,CAAC6B,IAAI,EAACF,SAAS,CAAC,4CAA4C,CAAAC,QAAA,cAC7DX,KAAA,QAAKU,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC7DX,KAAA,QAAAW,QAAA,eACIb,IAAA,CAACf,IAAI,CAAC8B,KAAK,EAACH,SAAS,CAAC,IAAI,CAAAC,QAAA,CAAEN,KAAK,CAAa,CAAC,CAC9CG,OAAO,cACJR,KAAA,QAAKU,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACtCb,IAAA,CAACb,OAAO,EAAC6B,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACL,SAAS,CAAC,MAAM,CAAE,CAAC,cACzDZ,IAAA,SAAAa,QAAA,CAAM,YAAU,CAAM,CAAC,EACtB,CAAC,cAENX,KAAA,QAAAW,QAAA,eACIb,IAAA,OAAIY,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEL,KAAK,CAAK,CAAC,CAChCC,IAAI,eAAIT,IAAA,UAAOY,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEJ,IAAI,CAAQ,CAAC,EACpD,CACR,EACA,CAAC,CACLE,IAAI,eAAIX,IAAA,QAAKY,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAEF,IAAI,CAAM,CAAC,EACrD,CAAC,CACC,CAAC,CACV,CAAC,EACV,CAED,KAAM,CAAAO,MAAM,CAAGA,CAAA,GAAM,CACjB,KAAM,CAAEC,CAAE,CAAC,CAAGtC,cAAc,CAAC,CAAC,CAC9B,KAAM,CAAC6B,OAAO,CAAEU,UAAU,CAAC,CAAGzC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC0C,KAAK,CAAEC,QAAQ,CAAC,CAAG3C,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAAC4C,YAAY,CAAEC,eAAe,CAAC,CAAG7C,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAC8C,cAAc,CAAEC,iBAAiB,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACgD,UAAU,CAAEC,aAAa,CAAC,CAAGjD,QAAQ,CAAC,KAAK,CAAC,CAEnD;AACA,KAAM,CAAAkD,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACAT,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,CAAAU,OAAO,CAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAG,+BAA+B,CACxEC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAEL,OAAO,CAAC,CAE/C,KAAM,CAAAM,YAAY,CAAG,KAAM,CAAAC,KAAK,CAACP,OAAO,CAAE,CACtCQ,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,SAAS,CACtBC,OAAO,CAAE,CACL,cAAc,CAAE,kBACpB,CACJ,CAAC,CAAC,CAEFN,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEC,YAAY,CAACK,MAAM,CAAC,CAEzD,GAAIL,YAAY,CAACM,EAAE,CAAE,CACjB,KAAM,CAAAC,UAAU,CAAG,KAAM,CAAAP,YAAY,CAACQ,IAAI,CAAC,CAAC,CAC5CV,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAEQ,UAAU,CAAC,CACjD,CAAC,IAAM,CACHT,OAAO,CAACb,KAAK,CAAC,kBAAkB,CAAEe,YAAY,CAACK,MAAM,CAAC,CAC1D,CAEA;AACA,KAAM,CAAAI,QAAQ,CAAGd,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAG,0CAA0C,CACpFC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAEU,QAAQ,CAAC,CAE3C,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAT,KAAK,CAACQ,QAAQ,CAAE,CACnCP,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,SAAS,CAAE;AACxBC,OAAO,CAAE,CACL,cAAc,CAAE,kBACpB,CACJ,CAAC,CAAC,CAEFN,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEW,QAAQ,CAACL,MAAM,CAAC,CAEhD,GAAI,CAACK,QAAQ,CAACJ,EAAE,CAAE,CACd,KAAM,CAAAK,SAAS,CAAG,KAAM,CAAAD,QAAQ,CAACE,IAAI,CAAC,CAAC,CACvCd,OAAO,CAACb,KAAK,CAAC,iBAAiB,CAAE0B,SAAS,CAAC,CAC3C,KAAM,IAAI,CAAAE,KAAK,CAAC,uBAAuBH,QAAQ,CAACL,MAAM,MAAMM,SAAS,EAAE,CAAC,CAC5E,CAEA,KAAM,CAAAG,MAAM,CAAG,KAAM,CAAAJ,QAAQ,CAACF,IAAI,CAAC,CAAC,CACpCV,OAAO,CAACC,GAAG,CAAC,eAAe,CAAEe,MAAM,CAAC,CAEpC,GAAIA,MAAM,CAACC,OAAO,CAAE,CAChB3B,eAAe,CAAC0B,MAAM,CAACE,IAAI,CAAC,CAC5B9B,QAAQ,CAAC,IAAI,CAAC,CAClB,CAAC,IAAM,CACHA,QAAQ,CAAC4B,MAAM,CAACG,OAAO,EAAI,gCAAgC,CAAC,CAChE,CAEA;AACA,KAAM,CAAAC,mBAAmB,CAAC,CAAC,CAE/B,CAAE,MAAOjC,KAAK,CAAE,CACZa,OAAO,CAACb,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvDC,QAAQ,CAAC,+CAA+C,CAAGD,KAAK,CAACgC,OAAO,CAAC,CAC7E,CAAC,OAAS,CACNjC,UAAU,CAAC,KAAK,CAAC,CACjBQ,aAAa,CAAC,KAAK,CAAC,CACxB,CACJ,CAAC,CAED;AACA,KAAM,CAAA0B,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACpC,KAAM,CAAAC,QAAQ,CAAGzD,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACyD,QAAQ,CAAE,OAEf,GAAI,CACA,KAAM,CAAEH,IAAI,CAAE,CAAEI,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAD,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC,CACxD,GAAI,CAACF,IAAI,CAAE,OAEX;AACA,KAAM,CAAEJ,IAAI,CAAE3B,cAAc,CAAEJ,KAAK,CAAEsC,eAAgB,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CAClEK,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,wBAAwB,CAAC,CAChCC,KAAK,CAAC,WAAW,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAC,CAAC,CACxCC,KAAK,CAAC,EAAE,CAAC,CAEd,GAAIL,eAAe,CAAE,CACjBzB,OAAO,CAACb,KAAK,CAAC,kCAAkC,CAAEsC,eAAe,CAAC,CACtE,CAAC,IAAM,CACH;AACAjC,iBAAiB,CAACD,cAAc,CAACwC,OAAO,CAAC,CAAC,CAAC,CAC/C,CACJ,CAAE,MAAO5C,KAAK,CAAE,CACZa,OAAO,CAACb,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CAC3D,CACJ,CAAC,CAEDzC,SAAS,CAAC,IAAM,CACZiD,iBAAiB,CAAC,CAAC,CACvB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAqC,aAAa,CAAGA,CAAA,GAAM,CACxBtC,aAAa,CAAC,IAAI,CAAC,CACnBC,iBAAiB,CAAC,CAAC,CACvB,CAAC,CAED,KAAM,CAAAsC,YAAY,CAAIC,GAAG,EAAK,CAC1B,GAAIA,GAAG,GAAK,IAAI,EAAIA,GAAG,GAAKC,SAAS,CAAE,MAAO,KAAK,CACnD,MAAO,IAAI,CAAAC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAE,CAClCC,qBAAqB,CAAE,CAAC,CACxBC,qBAAqB,CAAE,CAC3B,CAAC,CAAC,CAACC,MAAM,CAACN,GAAG,CAAC,CAClB,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,UAAU,EAAK,CAC/B,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CACpD,CAAC,CAED,GAAIzD,KAAK,CAAE,CACP,mBACIrB,IAAA,CAAClB,SAAS,EAACiG,KAAK,MAAAlE,QAAA,cACZb,IAAA,CAACjB,GAAG,EAAC6B,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBX,KAAA,CAAClB,GAAG,EAAA6B,QAAA,eACAb,IAAA,OAAAa,QAAA,CAAKM,CAAC,CAAC,sBAAsB,CAAC,CAAK,CAAC,cACpCnB,IAAA,CAACZ,KAAK,EAAAyB,QAAA,CAAEQ,KAAK,CAAQ,CAAC,EACrB,CAAC,CACL,CAAC,CACC,CAAC,CAEpB,CAEA,mBACInB,KAAA,CAACpB,SAAS,EAACiG,KAAK,MAAAlE,QAAA,eACZb,IAAA,CAACjB,GAAG,EAAC6B,SAAS,CAAC,MAAM,CAAAC,QAAA,cACjBb,IAAA,CAAChB,GAAG,EAAA6B,QAAA,cACAX,KAAA,QAAKU,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAC9Db,IAAA,OAAAa,QAAA,CAAKM,CAAC,CAAC,sBAAsB,CAAC,CAAK,CAAC,cACpCnB,IAAA,CAACX,MAAM,EACH2F,OAAO,CAAEd,aAAc,CACvBe,QAAQ,CAAEtD,UAAW,CAAAd,QAAA,CAEpBc,UAAU,cACPzB,KAAA,CAAAE,SAAA,EAAAS,QAAA,eACIb,IAAA,CAACb,OAAO,EAAC6B,SAAS,CAAC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACL,SAAS,CAAC,MAAM,CAAE,CAAC,CACxDO,CAAC,CAAC,YAAY,CAAC,EAClB,CAAC,CAEHA,CAAC,CAAC,SAAS,CACd,CACG,CAAC,EACR,CAAC,CACL,CAAC,CACL,CAAC,cAGNjB,KAAA,CAACnB,GAAG,EAAC6B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBb,IAAA,CAAChB,GAAG,EAACkG,EAAE,CAAE,CAAE,CAAArE,QAAA,cACPb,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEY,CAAC,CAAC,cAAc,CAAE,CACzBX,KAAK,CAAE2D,YAAY,CAAC5C,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE4D,YAAY,CAAE,CAChDzE,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNX,IAAA,CAAChB,GAAG,EAACkG,EAAE,CAAE,CAAE,CAAArE,QAAA,cACPb,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEY,CAAC,CAAC,uBAAuB,CAAE,CAClCX,KAAK,CAAE2D,YAAY,CAAC5C,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE6D,qBAAqB,CAAE,CACzD3E,IAAI,CAAC,KAAK,CACVC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNX,IAAA,CAAChB,GAAG,EAACkG,EAAE,CAAE,CAAE,CAAArE,QAAA,cACPb,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEY,CAAC,CAAC,eAAe,CAAE,CAC1BX,KAAK,CAAE2D,YAAY,CAAC5C,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE8D,aAAa,CAAE,CACjD3E,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNX,IAAA,CAAChB,GAAG,EAACkG,EAAE,CAAE,CAAE,CAAArE,QAAA,cACPb,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEY,CAAC,CAAC,cAAc,CAAE,CACzBX,KAAK,CAAE2D,YAAY,CAAC5C,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE+D,YAAY,CAAE,CAChD7E,IAAI,CAAC,KAAK,CACVC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,EACL,CAAC,cAENT,KAAA,CAACnB,GAAG,EAAC6B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBb,IAAA,CAAChB,GAAG,EAACkG,EAAE,CAAE,CAAE,CAAArE,QAAA,cACPb,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEY,CAAC,CAAC,mBAAmB,CAAE,CAC9BX,KAAK,CAAE2D,YAAY,CAAC5C,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEgE,aAAa,CAAE,CACjD9E,IAAI,CAAC,SAAS,CACdC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,QAAG,CACX,CAAC,CACD,CAAC,cACNX,IAAA,CAAChB,GAAG,EAACkG,EAAE,CAAE,CAAE,CAAArE,QAAA,cACPb,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEY,CAAC,CAAC,oBAAoB,CAAE,CAC/BX,KAAK,CAAE2D,YAAY,CAAC5C,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEiE,kBAAkB,CAAE,CACtD/E,IAAI,CAAC,KAAK,CACVC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNX,IAAA,CAAChB,GAAG,EAACkG,EAAE,CAAE,CAAE,CAAArE,QAAA,cACPb,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEY,CAAC,CAAC,yBAAyB,CAAE,CACpCX,KAAK,CAAE2D,YAAY,CAAC5C,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEkE,uBAAuB,CAAE,CAC3DhF,IAAI,CAAC,KAAK,CACVC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNX,IAAA,CAAChB,GAAG,EAACkG,EAAE,CAAE,CAAE,CAAArE,QAAA,cACPb,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEY,CAAC,CAAC,cAAc,CAAE,CACzBX,KAAK,CAAE2D,YAAY,CAAC5C,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEmE,YAAY,CAAE,CAChDhF,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,EACL,CAAC,cAGNT,KAAA,CAACnB,GAAG,EAAC6B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACjBb,IAAA,CAAChB,GAAG,EAACkG,EAAE,CAAE,CAAE,CAAArE,QAAA,cACPb,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEY,CAAC,CAAC,uBAAuB,CAAE,CAClCX,KAAK,CAAE2D,YAAY,CAAC5C,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEoE,qBAAqB,CAAE,CACzDlF,IAAI,CAAC,WAAW,CAChBC,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,cAAI,CACZ,CAAC,CACD,CAAC,cACNX,IAAA,CAAChB,GAAG,EAACkG,EAAE,CAAE,CAAE,CAAArE,QAAA,cACPb,IAAA,CAACK,QAAQ,EACLE,KAAK,CAAEY,CAAC,CAAC,cAAc,CAAE,CACzBX,KAAK,CAAE,CAAAe,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEqE,YAAY,GAAI,KAAM,CAC3ClF,OAAO,CAAEA,OAAQ,CACjBC,IAAI,CAAC,QAAG,CACX,CAAC,CACD,CAAC,EACL,CAAC,cAGNX,IAAA,CAACjB,GAAG,EAAA8B,QAAA,cACAb,IAAA,CAAChB,GAAG,EAAA6B,QAAA,cACAb,IAAA,CAACf,IAAI,EAAA4B,QAAA,cACDX,KAAA,CAACjB,IAAI,CAAC6B,IAAI,EAAAD,QAAA,eACNb,IAAA,CAACf,IAAI,CAAC8B,KAAK,EAAAF,QAAA,CAAEM,CAAC,CAAC,yBAAyB,CAAC,CAAa,CAAC,CACtDT,OAAO,cACJR,KAAA,QAAKU,SAAS,CAAC,aAAa,CAAAC,QAAA,eACxBb,IAAA,CAACb,OAAO,EAAC6B,SAAS,CAAC,QAAQ,CAAE,CAAC,cAC9BhB,IAAA,MAAGY,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEM,CAAC,CAAC,SAAS,CAAC,CAAI,CAAC,EACrC,CAAC,CACNI,YAAY,cACZvB,IAAA,CAACd,KAAK,EAAC2G,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAnF,QAAA,cACpCX,KAAA,UAAAW,QAAA,eACIX,KAAA,OAAAW,QAAA,eACIb,IAAA,OAAAa,QAAA,cAAIb,IAAA,WAAAa,QAAA,CAASM,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,CAAI,CAAC,cAC7CnB,IAAA,OAAAa,QAAA,CAAKsD,YAAY,CAAC5C,YAAY,CAAC4D,YAAY,CAAC,CAAK,CAAC,cAClDnF,IAAA,OAAAa,QAAA,cAAIb,IAAA,WAAAa,QAAA,CAASM,CAAC,CAAC,uBAAuB,CAAC,CAAS,CAAC,CAAI,CAAC,cACtDjB,KAAA,OAAAW,QAAA,EAAKsD,YAAY,CAAC5C,YAAY,CAAC6D,qBAAqB,CAAC,CAAC,MAAI,EAAI,CAAC,EAC/D,CAAC,cACLlF,KAAA,OAAAW,QAAA,eACIb,IAAA,OAAAa,QAAA,cAAIb,IAAA,WAAAa,QAAA,CAASM,CAAC,CAAC,eAAe,CAAC,CAAS,CAAC,CAAI,CAAC,cAC9CnB,IAAA,OAAAa,QAAA,CAAKsD,YAAY,CAAC5C,YAAY,CAAC8D,aAAa,CAAC,CAAK,CAAC,cACnDrF,IAAA,OAAAa,QAAA,cAAIb,IAAA,WAAAa,QAAA,CAASM,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,CAAI,CAAC,cAC7CjB,KAAA,OAAAW,QAAA,EAAKsD,YAAY,CAAC5C,YAAY,CAAC+D,YAAY,CAAC,CAAC,MAAI,EAAI,CAAC,EACtD,CAAC,cACLpF,KAAA,OAAAW,QAAA,eACIb,IAAA,OAAAa,QAAA,cAAIb,IAAA,WAAAa,QAAA,CAASM,CAAC,CAAC,mBAAmB,CAAC,CAAS,CAAC,CAAI,CAAC,cAClDjB,KAAA,OAAAW,QAAA,EAAKsD,YAAY,CAAC5C,YAAY,CAACgE,aAAa,CAAC,CAAC,UAAQ,EAAI,CAAC,cAC3DvF,IAAA,OAAAa,QAAA,cAAIb,IAAA,WAAAa,QAAA,CAASM,CAAC,CAAC,oBAAoB,CAAC,CAAS,CAAC,CAAI,CAAC,cACnDjB,KAAA,OAAAW,QAAA,EAAKsD,YAAY,CAAC5C,YAAY,CAACiE,kBAAkB,CAAC,CAAC,MAAI,EAAI,CAAC,EAC5D,CAAC,cACLtF,KAAA,OAAAW,QAAA,eACIb,IAAA,OAAAa,QAAA,cAAIb,IAAA,WAAAa,QAAA,CAASM,CAAC,CAAC,yBAAyB,CAAC,CAAS,CAAC,CAAI,CAAC,cACxDjB,KAAA,OAAAW,QAAA,EAAKsD,YAAY,CAAC5C,YAAY,CAACkE,uBAAuB,CAAC,CAAC,MAAI,EAAI,CAAC,cACjEzF,IAAA,OAAAa,QAAA,cAAIb,IAAA,WAAAa,QAAA,CAASM,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,CAAI,CAAC,cAC7CnB,IAAA,OAAAa,QAAA,CAAKsD,YAAY,CAAC5C,YAAY,CAACmE,YAAY,CAAC,CAAK,CAAC,EAClD,CAAC,cACLxF,KAAA,OAAAW,QAAA,eACIb,IAAA,OAAAa,QAAA,cAAIb,IAAA,WAAAa,QAAA,CAASM,CAAC,CAAC,uBAAuB,CAAC,CAAS,CAAC,CAAI,CAAC,cACtDjB,KAAA,OAAAW,QAAA,EAAKsD,YAAY,CAAC5C,YAAY,CAACoE,qBAAqB,CAAC,CAAC,YAAU,EAAI,CAAC,cACrE3F,IAAA,OAAAa,QAAA,cAAIb,IAAA,WAAAa,QAAA,CAASM,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,CAAI,CAAC,cAC7CnB,IAAA,OAAAa,QAAA,CAAKU,YAAY,CAACqE,YAAY,EAAI,KAAK,CAAK,CAAC,EAC7C,CAAC,cACL1F,KAAA,OAAAW,QAAA,eACIb,IAAA,OAAAa,QAAA,cAAIb,IAAA,WAAAa,QAAA,CAASM,CAAC,CAAC,cAAc,CAAC,CAAS,CAAC,CAAI,CAAC,cAC7CnB,IAAA,OAAIiG,OAAO,CAAC,GAAG,CAAApF,QAAA,CAAEU,YAAY,CAAC2E,UAAU,CAAG,GAAI,CAAArB,IAAI,CAACtD,YAAY,CAAC2E,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC,CAAG,KAAK,CAAK,CAAC,EAC3G,CAAC,EACF,CAAC,CACL,CAAC,cAERnG,IAAA,MAAAa,QAAA,CAAIM,CAAC,CAAC,mBAAmB,CAAC,CAAI,CACjC,EACM,CAAC,CACV,CAAC,CACN,CAAC,CACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}