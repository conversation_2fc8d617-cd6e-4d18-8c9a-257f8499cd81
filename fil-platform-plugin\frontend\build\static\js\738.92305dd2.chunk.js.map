{"version": 3, "file": "static/js/738.92305dd2.chunk.js", "mappings": "sMAOA,MAAMA,GAAgBC,EAAAA,EAAAA,GAAiB,MACvCD,EAAcE,YAAc,gBAC5B,MAAMC,EAA4BC,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDC,EAAS,SACTC,EACAC,GAAIC,EAAYV,KACbW,GACJN,EAEC,OADAG,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,kBACpBK,EAAAA,EAAAA,KAAKH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,MAC9BG,MAGPR,EAAaD,YAAc,eAC3B,U,cChBA,MAAMa,EAAyBX,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/CC,EAAS,SACTC,EACAC,GAAIC,EAAYM,EAAAA,KACbL,GACJN,EAEC,OADAG,GAAWI,EAAAA,EAAAA,IAAmBJ,EAAU,eACpBK,EAAAA,EAAAA,KAAKH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,MAC9BG,MAGPI,EAAUb,YAAc,YACxB,U,wBCRA,MAAMe,EAAqBb,EAAAA,WAAiB,CAACc,EAAmBZ,KAC9D,MAAM,SACJE,EAAQ,KACRW,GAAO,EAAI,WACXC,EAAa,cAAa,aAC1BC,EAAY,UACZd,EAAS,SACTe,EAAQ,QACRC,EAAU,UAAS,QACnBC,EAAO,YACPC,EAAW,WACXC,EAAaC,EAAAA,KACVhB,IACDiB,EAAAA,EAAAA,IAAgBV,EAAmB,CACrCC,KAAM,YAEFU,GAASjB,EAAAA,EAAAA,IAAmBJ,EAAU,SACtCsB,GAAcC,EAAAA,EAAAA,GAAiBC,IAC/BR,GACFA,GAAQ,EAAOQ,KAGbC,GAA4B,IAAfP,EAAsBC,EAAAA,EAAOD,EAC1CQ,GAAqBC,EAAAA,EAAAA,MAAM,MAAO,CACtCC,KAAM,WACDH,OAAqBI,EAAR1B,EAClBL,IAAKA,EACLC,UAAWO,IAAWP,EAAWsB,EAAQN,GAAW,GAAGM,KAAUN,IAAWE,GAAe,GAAGI,iBAC9FP,SAAU,CAACG,IAA4BZ,EAAAA,EAAAA,KAAKyB,EAAAA,EAAa,CACvDC,QAAST,EACT,aAAcV,EACdG,QAASF,IACPC,KAEN,OAAKW,GACepB,EAAAA,EAAAA,KAAKoB,EAAY,CACnCO,eAAe,KACZ7B,EACHL,SAAK+B,EACLI,GAAItB,EACJG,SAAUY,IANYf,EAAOe,EAAQ,OASzCjB,EAAMf,YAAc,QACpB,QAAewC,OAAOC,OAAO1B,EAAO,CAClC2B,KAAM7B,EACN8B,QAAS1C,G,4ICrDX,MA6LA,EA7LgB2C,KACZ,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,KAClCC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,KAClCG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,OACtCK,EAAWC,IAAgBN,EAAAA,EAAAA,UAAS,OACpCO,EAAcC,IAAmBR,EAAAA,EAAAA,UAAS,YAC1CS,EAASC,IAAcV,EAAAA,EAAAA,WAAS,IAChCW,EAAYC,IAAiBZ,EAAAA,EAAAA,WAAS,IACtCa,EAASC,IAAcd,EAAAA,EAAAA,UAAS,CAAEe,KAAM,GAAIC,KAAM,MAEzDC,EAAAA,EAAAA,WAAU,KACiBC,WACnB,MAAMC,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EAAU,OAEf,MAAQE,MAAM,KAAEC,UAAiBH,EAASI,KAAKC,UAC/C,IAAKF,EAED,YADAZ,GAAW,GAIf,MAAM,KAAEW,EAAI,MAAEI,SAAgBN,EACzBO,KAAK,qBACLC,OAAO,kEACPC,GAAG,UAAWN,EAAKO,IACnBC,SAEDL,GAAwB,aAAfA,EAAMM,MACfC,QAAQP,MAAM,6BAA8BA,GAC5CX,EAAW,CAAEC,KAAM,SAAUC,KAAMpB,EAAE,gCAC9ByB,IACPtB,EAAYsB,EAAKY,WAAa,IAC9B/B,EAAYmB,EAAKa,WAAa,IAC9B9B,EAAciB,EAAKc,cAAgB,MACnC7B,EAAae,EAAKe,aAAe,MACjC5B,EAAgBa,EAAKgB,eAAiB,YAE1C3B,GAAW,IAGf4B,IACD,IAEH,MAAMC,EAAmBA,CAAC1D,EAAG2D,KACrB3D,EAAE4D,OAAOC,OAAS7D,EAAE4D,OAAOC,MAAM,IACjCF,EAAS3D,EAAE4D,OAAOC,MAAM,KA8DhC,OAAIjC,GACO/C,EAAAA,EAAAA,KAAA,OAAAS,SAAMyB,EAAE,yBAIfZ,EAAAA,EAAAA,MAAC2D,EAAAA,EAAS,CAAAxE,SAAA,EACNT,EAAAA,EAAAA,KAAA,MAAIN,UAAU,OAAMe,SAAEyB,EAAE,uBACxBlC,EAAAA,EAAAA,KAACkF,EAAAA,EAAI,CAAAzE,UACDa,EAAAA,EAAAA,MAAC4D,EAAAA,EAAKC,KAAI,CAAA1E,SAAA,CACL0C,EAAQG,OAAQtD,EAAAA,EAAAA,KAACI,EAAAA,EAAK,CAACM,QAASyC,EAAQE,KAAK5C,SAAE0C,EAAQG,OAEtC,aAAjBT,IACG7C,EAAAA,EAAAA,KAACI,EAAAA,EAAK,CAACM,QAAQ,UAASD,SAAEyB,EAAE,kBAEd,YAAjBW,IACG7C,EAAAA,EAAAA,KAACI,EAAAA,EAAK,CAACM,QAAQ,OAAMD,SAAEyB,EAAE,wBAEX,aAAjBW,IACG7C,EAAAA,EAAAA,KAACI,EAAAA,EAAK,CAACM,QAAQ,SAAQD,SAAEyB,EAAE,mBAG/BZ,EAAAA,EAAAA,MAAC8D,EAAAA,EAAI,CAACC,SA/ED7B,UACjBrC,EAAEmE,iBACFpC,GAAc,GACdE,EAAW,CAAEC,KAAM,GAAIC,KAAM,KAE7B,IACI,MAAMiC,EAAW,IAAIC,SACrBD,EAASE,OAAO,YAAarD,GAC7BmD,EAASE,OAAO,YAAalD,GAGzBE,aAAsBiD,MACtBH,EAASE,OAAO,eAAgBhD,GAGhCE,aAAqB+C,MACrBH,EAASE,OAAO,cAAe9C,GAGnC,MAAMgD,QAAiBC,MAAM,GAAGC,OAAOC,OAAOC,mBAAoB,CAC9DC,OAAQ,OACRC,KAAMV,EACNW,QAAS,CACL,aAAcL,OAAOC,OAAOK,SAI9BC,QAAeT,EAASU,OAE9B,IAAKD,EAAOE,QACR,MAAM,IAAIC,MAAMH,EAAOjD,SAAW,wBAIlCiD,EAAOpB,OAASoB,EAAOpB,MAAMP,cAC7B/B,EAAc0D,EAAOpB,MAAMP,aAAa+B,KAGxCJ,EAAOpB,OAASoB,EAAOpB,MAAMN,aAC7B9B,EAAawD,EAAOpB,MAAMN,YAAY8B,KAG1CpD,EAAW,CAAEC,KAAM,UAAWC,KAAMpB,EAAE,wBACtCY,EAAgB,WAGZsD,EAAOK,QAAUL,EAAOK,OAAOC,OAAS,GACxCpC,QAAQqC,KAAK,mBAAoBP,EAAOK,OAGhD,CAAE,MAAO1C,GACLO,QAAQP,MAAM,wBAAyBA,GACvCX,EAAW,CAAEC,KAAM,SAAUC,KAAMpB,EAAE,wBAA0B,KAAO6B,EAAMZ,SAChF,CAEAD,GAAc,IAwB2BzC,SAAA,EACzBa,EAAAA,EAAAA,MAAC8D,EAAAA,EAAKwB,MAAK,CAAClH,UAAU,OAAMe,SAAA,EACxBT,EAAAA,EAAAA,KAACoF,EAAAA,EAAKyB,MAAK,CAAApG,SAAEyB,EAAE,gBACflC,EAAAA,EAAAA,KAACoF,EAAAA,EAAK0B,QAAO,CACTzD,KAAK,OACL0D,MAAO3E,EACP4E,SAAW7F,GAAMkB,EAAYlB,EAAE4D,OAAOgC,OACtCE,UAAQ,EACRC,SAA2B,YAAjBrE,GAA+C,aAAjBA,QAGhDvB,EAAAA,EAAAA,MAAC8D,EAAAA,EAAKwB,MAAK,CAAClH,UAAU,OAAMe,SAAA,EACxBT,EAAAA,EAAAA,KAACoF,EAAAA,EAAKyB,MAAK,CAAApG,SAAEyB,EAAE,gBACflC,EAAAA,EAAAA,KAACoF,EAAAA,EAAK0B,QAAO,CACTzD,KAAK,OACL0D,MAAOxE,EACPyE,SAAW7F,GAAMqB,EAAYrB,EAAE4D,OAAOgC,OACtCE,UAAQ,EACRC,SAA2B,YAAjBrE,GAA+C,aAAjBA,QAGhDvB,EAAAA,EAAAA,MAAC8D,EAAAA,EAAKwB,MAAK,CAAClH,UAAU,OAAMe,SAAA,EACxBT,EAAAA,EAAAA,KAACoF,EAAAA,EAAKyB,MAAK,CAAApG,SAAEyB,EAAE,eACflC,EAAAA,EAAAA,KAACoF,EAAAA,EAAK0B,QAAO,CACTzD,KAAK,OACL2D,SAAW7F,GAAM0D,EAAiB1D,EAAGuB,GACrCyE,OAAO,UACPD,SAA2B,YAAjBrE,GAA+C,aAAjBA,IAE3CJ,GAAoC,kBAAfA,IAClBzC,EAAAA,EAAAA,KAAA,OAAKoH,IAAK3E,EAAY4E,IAAI,WAAW3H,UAAU,qBAAqB4H,MAAO,CAAEC,SAAU,eAG/FjG,EAAAA,EAAAA,MAAC8D,EAAAA,EAAKwB,MAAK,CAAClH,UAAU,OAAMe,SAAA,EACxBT,EAAAA,EAAAA,KAACoF,EAAAA,EAAKyB,MAAK,CAAApG,SAAEyB,EAAE,cACflC,EAAAA,EAAAA,KAACoF,EAAAA,EAAK0B,QAAO,CACTzD,KAAK,OACL2D,SAAW7F,GAAM0D,EAAiB1D,EAAGyB,GACrCuE,OAAO,UACPD,SAA2B,YAAjBrE,GAA+C,aAAjBA,IAE3CF,GAAkC,kBAAdA,IACjB3C,EAAAA,EAAAA,KAAA,OAAKoH,IAAKzE,EAAW0E,IAAI,UAAU3H,UAAU,qBAAqB4H,MAAO,CAAEC,SAAU,eAI7FvH,EAAAA,EAAAA,KAACwH,EAAAA,EAAM,CACH9G,QAAQ,UACR2C,KAAK,SACL6D,SAAUjE,GAA+B,YAAjBJ,GAA+C,aAAjBA,EAA4BpC,SAEpEyB,EAAbe,EAAe,aAAkB,8B", "sources": ["../node_modules/react-bootstrap/esm/AlertHeading.js", "../node_modules/react-bootstrap/esm/AlertLink.js", "../node_modules/react-bootstrap/esm/Alert.js", "pages/customer/KycPage.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nDivStyledAsH4.displayName = 'DivStyledAsH4';\nconst AlertHeading = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-heading');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertHeading.displayName = 'AlertHeading';\nexport default AlertHeading;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AlertLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertLink.displayName = 'AlertLink';\nexport default AlertLink;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AlertHeading from './AlertHeading';\nimport AlertLink from './AlertLink';\nimport Fade from './Fade';\nimport CloseButton from './CloseButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Alert = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    bsPrefix,\n    show = true,\n    closeLabel = 'Close alert',\n    closeVariant,\n    className,\n    children,\n    variant = 'primary',\n    onClose,\n    dismissible,\n    transition = Fade,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    show: 'onClose'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'alert');\n  const handleClose = useEventCallback(e => {\n    if (onClose) {\n      onClose(false, e);\n    }\n  });\n  const Transition = transition === true ? Fade : transition;\n  const alert = /*#__PURE__*/_jsxs(\"div\", {\n    role: \"alert\",\n    ...(!Transition ? props : undefined),\n    ref: ref,\n    className: classNames(className, prefix, variant && `${prefix}-${variant}`, dismissible && `${prefix}-dismissible`),\n    children: [dismissible && /*#__PURE__*/_jsx(CloseButton, {\n      onClick: handleClose,\n      \"aria-label\": closeLabel,\n      variant: closeVariant\n    }), children]\n  });\n  if (!Transition) return show ? alert : null;\n  return /*#__PURE__*/_jsx(Transition, {\n    unmountOnExit: true,\n    ...props,\n    ref: undefined,\n    in: show,\n    children: alert\n  });\n});\nAlert.displayName = 'Alert';\nexport default Object.assign(Alert, {\n  Link: AlertLink,\n  Heading: AlertHeading\n});", "\nimport React, { useState, useEffect } from 'react';\nimport { Container, Form, Button, Card, Alert } from 'react-bootstrap';\nimport { getSupabase } from '../../supabaseClient';\nimport { useTranslation } from 'react-i18next';\n\nconst KycPage = () => {\n    const { t } = useTranslation();\n    const [realName, setRealName] = useState('');\n    const [idNumber, setIdNumber] = useState('');\n    const [idImgFront, setIdImgFront] = useState(null);\n    const [idImgBack, setIdImgBack] = useState(null);\n    const [verifyStatus, setVerifyStatus] = useState('pending'); // or 'approved', 'rejected'\n    const [loading, setLoading] = useState(true);\n    const [submitting, setSubmitting] = useState(false);\n    const [message, setMessage] = useState({ type: '', text: '' });\n\n    useEffect(() => {\n        const fetchKycStatus = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) {\n                setLoading(false);\n                return;\n            }\n\n            const { data, error } = await supabase\n                .from('customer_profiles')\n                .select('real_name, id_number, id_img_front, id_img_back, verify_status')\n                .eq('user_id', user.id)\n                .single();\n\n            if (error && error.code !== 'PGRST116') { // PGRST116 means no rows found\n                console.error('Error fetching KYC status:', error);\n                setMessage({ type: 'danger', text: t('failed_to_load_kyc_status') });\n            } else if (data) {\n                setRealName(data.real_name || '');\n                setIdNumber(data.id_number || '');\n                setIdImgFront(data.id_img_front || null);\n                setIdImgBack(data.id_img_back || null);\n                setVerifyStatus(data.verify_status || 'pending');\n            }\n            setLoading(false);\n        };\n\n        fetchKycStatus();\n    }, []);\n\n    const handleFileChange = (e, setImage) => {\n        if (e.target.files && e.target.files[0]) {\n            setImage(e.target.files[0]);\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setSubmitting(true);\n        setMessage({ type: '', text: '' });\n\n        try {\n            const formData = new FormData();\n            formData.append('real_name', realName);\n            formData.append('id_number', idNumber);\n\n            // Add files if they are new File objects\n            if (idImgFront instanceof File) {\n                formData.append('id_img_front', idImgFront);\n            }\n\n            if (idImgBack instanceof File) {\n                formData.append('id_img_back', idImgBack);\n            }\n\n            const response = await fetch(`${window.wpData.apiUrl}submit-kyc`, {\n                method: 'POST',\n                body: formData,\n                headers: {\n                    'X-WP-Nonce': window.wpData.nonce\n                }\n            });\n\n            const result = await response.json();\n\n            if (!result.success) {\n                throw new Error(result.message || 'Failed to submit KYC');\n            }\n\n            // Update local state with new image URLs if uploaded\n            if (result.files && result.files.id_img_front) {\n                setIdImgFront(result.files.id_img_front.url);\n            }\n\n            if (result.files && result.files.id_img_back) {\n                setIdImgBack(result.files.id_img_back.url);\n            }\n\n            setMessage({ type: 'success', text: t('kyc_submit_success') });\n            setVerifyStatus('pending');\n\n            // Show any upload warnings if present\n            if (result.errors && result.errors.length > 0) {\n                console.warn('Upload warnings:', result.errors);\n            }\n\n        } catch (error) {\n            console.error('KYC submission error:', error);\n            setMessage({ type: 'danger', text: t('failed_to_submit_kyc') + ': ' + error.message });\n        }\n\n        setSubmitting(false);\n    };\n\n    if (loading) {\n        return <div>{t('loading_kyc_status')}</div>;\n    }\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('kyc_verification')}</h2>\n            <Card>\n                <Card.Body>\n                    {message.text && <Alert variant={message.type}>{message.text}</Alert>}\n                    \n                    {verifyStatus === 'approved' && (\n                        <Alert variant=\"success\">{t('kyc_approved')}</Alert>\n                    )}\n                    {verifyStatus === 'pending' && (\n                        <Alert variant=\"info\">{t('kyc_pending_review')}</Alert>\n                    )}\n                    {verifyStatus === 'rejected' && (\n                        <Alert variant=\"danger\">{t('kyc_rejected')}</Alert>\n                    )}\n\n                    <Form onSubmit={handleSubmit}>\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label>{t('real_name')}</Form.Label>\n                            <Form.Control \n                                type=\"text\" \n                                value={realName} \n                                onChange={(e) => setRealName(e.target.value)} \n                                required \n                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}\n                            />\n                        </Form.Group>\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label>{t('id_number')}</Form.Label>\n                            <Form.Control \n                                type=\"text\" \n                                value={idNumber} \n                                onChange={(e) => setIdNumber(e.target.value)} \n                                required \n                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}\n                            />\n                        </Form.Group>\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label>{t('id_front')}</Form.Label>\n                            <Form.Control \n                                type=\"file\" \n                                onChange={(e) => handleFileChange(e, setIdImgFront)} \n                                accept=\"image/*\" \n                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}\n                            />\n                            {idImgFront && typeof idImgFront === 'string' && (\n                                <img src={idImgFront} alt=\"ID Front\" className=\"img-thumbnail mt-2\" style={{ maxWidth: '200px' }} />\n                            )}\n                        </Form.Group>\n                        <Form.Group className=\"mb-3\">\n                            <Form.Label>{t('id_back')}</Form.Label>\n                            <Form.Control \n                                type=\"file\" \n                                onChange={(e) => handleFileChange(e, setIdImgBack)} \n                                accept=\"image/*\" \n                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}\n                            />\n                            {idImgBack && typeof idImgBack === 'string' && (\n                                <img src={idImgBack} alt=\"ID Back\" className=\"img-thumbnail mt-2\" style={{ maxWidth: '200px' }} />\n                            )}\n                        </Form.Group>\n                        \n                        <Button \n                            variant=\"primary\" \n                            type=\"submit\" \n                            disabled={submitting || verifyStatus === 'pending' || verifyStatus === 'approved'}\n                        >\n                            {submitting ? t('submitting') : t('submit_review')}\n                        </Button>\n                    </Form>\n                </Card.Body>\n            </Card>\n        </Container>\n    );\n};\n\nexport default KycPage;\n"], "names": ["DivStyledAsH4", "divWithClassName", "displayName", "AlertHeading", "React", "_ref", "ref", "className", "bsPrefix", "as", "Component", "props", "useBootstrapPrefix", "_jsx", "classNames", "AlertLink", "<PERSON><PERSON>", "<PERSON><PERSON>", "uncontrolledProps", "show", "<PERSON><PERSON><PERSON><PERSON>", "closeVariant", "children", "variant", "onClose", "dismissible", "transition", "Fade", "useUncontrolled", "prefix", "handleClose", "useEventCallback", "e", "Transition", "alert", "_jsxs", "role", "undefined", "CloseButton", "onClick", "unmountOnExit", "in", "Object", "assign", "Link", "Heading", "KycPage", "t", "useTranslation", "realName", "setRealName", "useState", "idNumber", "setIdNumber", "idImgFront", "setIdImgFront", "idImgBack", "setIdImgBack", "verifyStatus", "setVerifyStatus", "loading", "setLoading", "submitting", "setSubmitting", "message", "setMessage", "type", "text", "useEffect", "async", "supabase", "getSupabase", "data", "user", "auth", "getUser", "error", "from", "select", "eq", "id", "single", "code", "console", "real_name", "id_number", "id_img_front", "id_img_back", "verify_status", "fetchKycStatus", "handleFileChange", "setImage", "target", "files", "Container", "Card", "Body", "Form", "onSubmit", "preventDefault", "formData", "FormData", "append", "File", "response", "fetch", "window", "wpData", "apiUrl", "method", "body", "headers", "nonce", "result", "json", "success", "Error", "url", "errors", "length", "warn", "Group", "Label", "Control", "value", "onChange", "required", "disabled", "accept", "src", "alt", "style", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "sourceRoot": ""}