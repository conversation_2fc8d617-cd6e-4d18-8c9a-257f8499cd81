<?php
// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

// This file will now be much simpler.
class FIL_Platform_API {

    public function __construct() {
        add_action('rest_api_init', [$this, 'register_routes']);
    }

    public function register_routes() {
        register_rest_route('fil-platform/v1', '/config', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$this, 'get_supabase_config'],
                'permission_callback' => '__return_true', // Publicly accessible
            ],
        ]);

        // Add manual scraper trigger endpoint
        register_rest_route('fil-platform/v1', '/scrape-filfox', [
            [
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => [$this, 'manual_scrape_filfox'],
                'permission_callback' => [$this, 'check_admin_permission'],
            ],
        ]);

        // Add scraper status endpoint
        register_rest_route('fil-platform/v1', '/scraper-status', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$this, 'get_scraper_status'],
                'permission_callback' => [$this, 'check_admin_permission'],
            ],
        ]);

        // Add real-time filfox data endpoint
        register_rest_route('fil-platform/v1', '/filfox-realtime', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$this, 'get_filfox_realtime_data'],
                'permission_callback' => '__return_true', // Allow public access for now
            ],
        ]);

        // Add test endpoint
        register_rest_route('fil-platform/v1', '/test', [
            [
                'methods'             => WP_REST_Server::READABLE,
                'callback'            => [$this, 'test_endpoint'],
                'permission_callback' => '__return_true',
            ],
        ]);

        // Add KYC file upload endpoint
        register_rest_route('fil-platform/v1', '/upload-kyc-image', [
            [
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => [$this, 'upload_kyc_image'],
                'permission_callback' => [$this, 'check_user_permission'],
            ],
        ]);

        // Add KYC submission endpoint
        register_rest_route('fil-platform/v1', '/submit-kyc', [
            [
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => [$this, 'submit_kyc'],
                'permission_callback' => [$this, 'check_user_permission'],
            ],
        ]);
    }

    public function get_supabase_config() {
        // IMPORTANT: These values should be stored securely,
        // for example, in wp-config.php, not directly in the code.
        // For this example, I will define them here.
        // The user will need to replace these with their actual Supabase credentials.
        $supabase_url = get_option('fil_platform_supabase_url', 'YOUR_SUPABASE_URL');
        $supabase_anon_key = get_option('fil_platform_supabase_anon_key', 'YOUR_SUPABASE_ANON_KEY');

        if ($supabase_url === 'YOUR_SUPABASE_URL' || $supabase_anon_key === 'YOUR_SUPABASE_ANON_KEY') {
            return new WP_Error(
                'supabase_not_configured',
                'Supabase URL and Key are not configured in WordPress settings.',
                ['status' => 500]
            );
        }

        return new WP_REST_Response([
            'url' => $supabase_url,
            'anonKey' => $supabase_anon_key,
        ], 200);
    }

    /**
     * Check admin permission
     */
    public function check_admin_permission() {
        return current_user_can('manage_options');
    }

    /**
     * Check user permission (any logged-in user)
     */
    public function check_user_permission() {
        return is_user_logged_in();
    }

    /**
     * Test endpoint
     */
    public function test_endpoint($request) {
        return new WP_REST_Response([
            'success' => true,
            'message' => 'API is working!',
            'timestamp' => current_time('mysql')
        ], 200);
    }

    /**
     * Manual trigger for filfox scraper (via REST API)
     */
    public function manual_scrape_filfox($request) {
        // Get the scraper instance and run it
        global $fil_platform_scraper;

        if (!$fil_platform_scraper) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Scraper not initialized'
            ], 500);
        }

        try {
            // Call the scraper's test method
            $result = $this->call_scraper_method();

            if ($result['success']) {
                return new WP_REST_Response([
                    'success' => true,
                    'message' => 'Filfox data scraped successfully',
                    'data' => $result['data']
                ], 200);
            } else {
                return new WP_REST_Response([
                    'success' => false,
                    'message' => 'Failed to scrape filfox data',
                    'error' => $result['error']
                ], 500);
            }

        } catch (Exception $e) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Exception occurred',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get scraper status
     */
    public function get_scraper_status($request) {
        $next_scheduled = wp_next_scheduled('fil_platform_filfox_scraper');
        $last_run = get_option('fil_platform_last_scrape_run', 'Never');
        $last_success = get_option('fil_platform_last_scrape_success', 'Never');

        return new WP_REST_Response([
            'next_scheduled' => $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled) . ' UTC' : 'Not scheduled',
            'next_scheduled_jst' => $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled + 9 * 3600) . ' JST' : 'Not scheduled',
            'last_run' => $last_run,
            'last_success' => $last_success,
            'cron_enabled' => !defined('DISABLE_WP_CRON') || !DISABLE_WP_CRON
        ], 200);
    }

    /**
     * Get real-time filfox data
     */
    public function get_filfox_realtime_data($request) {
        error_log('FIL Platform: Real-time API endpoint called');

        try {
            // Scrape data from filfox.info in real-time
            $result = $this->scrape_filfox_realtime();

            error_log('FIL Platform: Scrape result: ' . json_encode($result));

            if ($result['success']) {
                return new WP_REST_Response([
                    'success' => true,
                    'data' => $result['data'],
                    'timestamp' => current_time('mysql')
                ], 200);
            } else {
                return new WP_REST_Response([
                    'success' => false,
                    'message' => 'Failed to fetch real-time data',
                    'error' => $result['error']
                ], 500);
            }

        } catch (Exception $e) {
            error_log('FIL Platform: Exception in real-time API: ' . $e->getMessage());
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Exception occurred',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Helper method to call scraper functionality
     */
    private function call_scraper_method() {
        // Directly implement scraping logic here for API access
        update_option('fil_platform_last_scrape_run', current_time('mysql'));

        try {
            $response = wp_remote_get('https://filfox.info/en', [
                'timeout' => 30,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'headers' => [
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.5',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Connection' => 'keep-alive',
                ]
            ]);

            if (is_wp_error($response)) {
                throw new Exception('HTTP request failed: ' . $response->get_error_message());
            }

            $body = wp_remote_retrieve_body($response);
            $status_code = wp_remote_retrieve_response_code($response);

            if ($status_code !== 200) {
                throw new Exception('HTTP request returned status code: ' . $status_code);
            }

            if (empty($body)) {
                throw new Exception('Empty response body');
            }

            // Parse the HTML to extract data
            $data = $this->parse_filfox_html($body);

            if (!$data['mining_reward']) {
                throw new Exception('No valid data found in the response');
            }

            update_option('fil_platform_last_scrape_success', current_time('mysql'));

            return [
                'success' => true,
                'data' => $data
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Parse HTML content (simplified version for API)
     */
    private function parse_filfox_html($html) {
        $mining_reward = null;

        // Remove script and style tags
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
        $html = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $html);

        // Try to find mining reward
        if (preg_match('/24h\s+Average\s+Mining\s+Reward[^0-9]*([0-9]+\.?[0-9]*)\s*FIL\/TiB/i', $html, $matches)) {
            $reward = (float) $matches[1];
            if ($reward > 0 && $reward < 10) {
                $mining_reward = $reward;
            }
        }

        // Alternative pattern for mining reward
        if (!$mining_reward && preg_match('/([0-9]*\.?[0-9]+)\s*FIL\/TiB/i', $html, $matches)) {
            $reward = (float) $matches[1];
            if ($reward > 0 && $reward < 10) {
                $mining_reward = $reward;
            }
        }

        return [
            'mining_reward' => $mining_reward ?: 0.0,
            'scraped_at' => current_time('mysql')
        ];
    }

    /**
     * Scrape filfox data in real-time with all network statistics
     */
    private function scrape_filfox_realtime() {
        try {
            // Use WordPress HTTP API to fetch the page
            $response = wp_remote_get('https://filfox.info/en', [
                'timeout' => 30,
                'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'headers' => [
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'en-US,en;q=0.5',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Connection' => 'keep-alive',
                ]
            ]);

            if (is_wp_error($response)) {
                throw new Exception('HTTP request failed: ' . $response->get_error_message());
            }

            $body = wp_remote_retrieve_body($response);
            $status_code = wp_remote_retrieve_response_code($response);

            if ($status_code !== 200) {
                throw new Exception('HTTP request returned status code: ' . $status_code);
            }

            if (empty($body)) {
                throw new Exception('Empty response body');
            }

            // Parse the HTML to extract all network data
            $data = $this->parse_filfox_realtime_html($body);

            // Validate that we have at least some essential data
            if (!$data['mining_reward'] && !$data['block_height'] && !$data['network_storage_power']) {
                throw new Exception('No valid data found in the response');
            }

            return [
                'success' => true,
                'data' => $data
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Parse HTML content to extract all network statistics for real-time display
     */
    private function parse_filfox_realtime_html($html) {
        // Initialize all data fields
        $data = [
            'block_height' => null,
            'latest_block' => null,
            'network_storage_power' => null,
            'active_miners' => null,
            'block_reward' => null,
            'mining_reward' => null,
            'fil_production_24h' => null,
            'sector_initial_pledge' => null,
            'total_pledge_collateral' => null,
            'messages_24h' => null,
            'scraped_at' => current_time('mysql')
        ];

        // Remove script and style tags to avoid false matches
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
        $html = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $html);

        // Debug: Log a portion of the HTML to understand the structure
        error_log('FIL Platform: HTML sample (first 3000 chars): ' . substr($html, 0, 3000));

        // Look for specific text patterns in the HTML
        if (strpos($html, 'Block Height') !== false) {
            error_log('FIL Platform: Found "Block Height" in HTML');
        }
        if (strpos($html, 'Network Storage Power') !== false) {
            error_log('FIL Platform: Found "Network Storage Power" in HTML');
        }
        if (strpos($html, 'Active Miners') !== false) {
            error_log('FIL Platform: Found "Active Miners" in HTML');
        }

        // Extract Block Height - based on actual HTML structure
        if (preg_match('/Block\s+Height.*?<div[^>]*>\s*([0-9,]+)\s*<\/div>/is', $html, $matches)) {
            $data['block_height'] = (int) str_replace(',', '', $matches[1]);
            error_log('FIL Platform: Found block height: ' . $data['block_height']);
        }

        // Extract Latest Block (time ago) - based on actual HTML structure
        if (preg_match('/Latest\s+Block.*?<span[^>]*>\s*([0-9]+\s*(?:min|sec|hour|day)s?\s*ago)\s*<\/span>/is', $html, $matches)) {
            $data['latest_block'] = trim($matches[1]);
            error_log('FIL Platform: Found latest block: ' . $data['latest_block']);
        } elseif (preg_match('/Latest\s+Block.*?>\s*([0-9]+\s*(?:min|sec|hour|day)s?\s*ago)\s*</is', $html, $matches)) {
            $data['latest_block'] = trim($matches[1]);
            error_log('FIL Platform: Found latest block (alternative pattern): ' . $data['latest_block']);
        }

        // Extract Network Storage Power (EiB) - based on actual HTML structure
        if (preg_match('/Network\s+Storage\s+Power.*?<div[^>]*>\s*([0-9]+\.?[0-9]*)\s*EiB\s*<\/div>/is', $html, $matches)) {
            $data['network_storage_power'] = (float) $matches[1];
            error_log('FIL Platform: Found network storage power: ' . $data['network_storage_power']);
        }

        // Extract Active Miners - based on actual HTML structure
        if (preg_match('/Active\s+Miners.*?<div[^>]*>\s*([0-9,]+)\s*<\/div>/is', $html, $matches)) {
            $data['active_miners'] = (int) str_replace(',', '', $matches[1]);
            error_log('FIL Platform: Found active miners: ' . $data['active_miners']);
        }

        // Extract Block Reward - based on actual HTML structure
        if (preg_match('/Block\s+Reward.*?<div[^>]*>\s*([0-9]+\.?[0-9]*)\s*FIL\s*<\/div>/is', $html, $matches)) {
            $data['block_reward'] = (float) $matches[1];
            error_log('FIL Platform: Found block reward: ' . $data['block_reward']);
        }

        // Extract 24h Average Mining Reward
        if (preg_match('/24h\s+Average\s+Mining\s+Reward[^0-9]*([0-9]+\.?[0-9]*)\s*FIL\/TiB/i', $html, $matches)) {
            $data['mining_reward'] = (float) $matches[1];
        }

        // Extract 24h FIL Production - based on actual HTML structure
        if (preg_match('/24h\s+FIL\s+Production.*?<div[^>]*>\s*([0-9,]+)\s*FIL\s*<\/div>/is', $html, $matches)) {
            $data['fil_production_24h'] = (int) str_replace(',', '', $matches[1]);
            error_log('FIL Platform: Found 24h FIL production: ' . $data['fil_production_24h']);
        }

        // Extract Current Sector Initial Pledge - based on actual HTML structure
        if (preg_match('/Current\s+Sector\s+Initial\s+Pledge.*?<div[^>]*>\s*([0-9]+\.?[0-9]*)\s*FIL\/32GiB\s*<\/div>/is', $html, $matches)) {
            $data['sector_initial_pledge'] = (float) $matches[1];
            error_log('FIL Platform: Found sector initial pledge: ' . $data['sector_initial_pledge']);
        }

        // Extract Total Pledge Collateral - based on actual HTML structure
        if (preg_match('/Total\s+Pledge\s+Collateral.*?<div[^>]*>\s*([0-9,]+)\s*FIL\s*<\/div>/is', $html, $matches)) {
            $data['total_pledge_collateral'] = (int) str_replace(',', '', $matches[1]);
            error_log('FIL Platform: Found total pledge collateral: ' . $data['total_pledge_collateral']);
        }

        // Extract 24h Messages - based on actual HTML structure
        if (preg_match('/24h\s+Messages.*?<div[^>]*>\s*([0-9,]+)\s*<\/div>/is', $html, $matches)) {
            $data['messages_24h'] = (int) str_replace(',', '', $matches[1]);
            error_log('FIL Platform: Found 24h messages: ' . $data['messages_24h']);
        }

        // Fallback patterns for mining reward if not found above
        if (!$data['mining_reward']) {
            // Pattern 2: Look for decimal numbers followed by "FIL/TiB"
            if (preg_match('/([0-9]*\.?[0-9]+)\s*FIL\/TiB/i', $html, $matches)) {
                $reward = (float) $matches[1];
                if ($reward > 0 && $reward < 10) {
                    $data['mining_reward'] = $reward;
                }
            }
        }

        // Additional fallback patterns based on actual HTML structure

        // Try alternative pattern for Block Height (looking for large numbers with commas)
        if (!$data['block_height'] || $data['block_height'] < 1000000) {
            if (preg_match('/([0-9]{1,3}(?:,[0-9]{3})+)/', $html, $matches)) {
                $potential_height = (int) str_replace(',', '', $matches[1]);
                if ($potential_height > 5000000) { // Current block height should be > 5M
                    $data['block_height'] = $potential_height;
                    error_log('FIL Platform: Found block height via fallback: ' . $potential_height);
                }
            }
        }

        // Try alternative pattern for Total Pledge Collateral (very large numbers)
        if (!$data['total_pledge_collateral']) {
            if (preg_match('/([0-9]{2,3}(?:,[0-9]{3}){2,})\s*FIL/', $html, $matches)) {
                $potential_collateral = (int) str_replace(',', '', $matches[1]);
                if ($potential_collateral > 100000000) { // Should be > 100M
                    $data['total_pledge_collateral'] = $potential_collateral;
                    error_log('FIL Platform: Found total pledge collateral via fallback: ' . $potential_collateral);
                }
            }
        }

        // Try alternative pattern for 24h Messages (large numbers)
        if (!$data['messages_24h'] || $data['messages_24h'] < 100000) {
            if (preg_match('/([0-9]{2,3}(?:,[0-9]{3})+)(?!\s*FIL)/', $html, $matches)) {
                $potential_messages = (int) str_replace(',', '', $matches[1]);
                if ($potential_messages > 100000 && $potential_messages < 1000000) { // Reasonable range for 24h messages
                    $data['messages_24h'] = $potential_messages;
                    error_log('FIL Platform: Found 24h messages via fallback: ' . $potential_messages);
                }
            }
        }

        // Try alternative pattern for 24h FIL Production
        if (!$data['fil_production_24h']) {
            if (preg_match('/([0-9]{2,3}(?:,[0-9]{3})*)\s*FIL(?!\/)/', $html, $matches)) {
                $potential_production = (int) str_replace(',', '', $matches[1]);
                if ($potential_production > 50000 && $potential_production < 200000) { // Reasonable range
                    $data['fil_production_24h'] = $potential_production;
                    error_log('FIL Platform: Found 24h FIL production via fallback: ' . $potential_production);
                }
            }
        }

        // Try alternative pattern for Block Reward
        if (!$data['block_reward']) {
            if (preg_match('/([0-9]+\.[0-9]{4})\s*FIL/', $html, $matches)) {
                $potential_reward = (float) $matches[1];
                if ($potential_reward > 1 && $potential_reward < 10) { // Reasonable range for block reward
                    $data['block_reward'] = $potential_reward;
                    error_log('FIL Platform: Found block reward via fallback: ' . $potential_reward);
                }
            }
        }

        // Log final extracted data
        error_log('FIL Platform: Final extracted data: ' . json_encode($data));

        return $data;
    }

    /**
     * Upload KYC image to WordPress media library
     */
    public function upload_kyc_image($request) {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'User not logged in'
            ], 401);
        }

        // Check if files were uploaded
        if (empty($_FILES)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'No files uploaded'
            ], 400);
        }

        $uploaded_files = [];
        $errors = [];

        // Process each uploaded file
        foreach ($_FILES as $file_key => $file) {
            if ($file['error'] !== UPLOAD_ERR_OK) {
                $errors[] = "Error uploading {$file_key}: " . $this->get_upload_error_message($file['error']);
                continue;
            }

            // Validate file type
            $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!in_array($file['type'], $allowed_types)) {
                $errors[] = "Invalid file type for {$file_key}. Only JPEG, PNG, and GIF are allowed.";
                continue;
            }

            // Validate file size (max 5MB)
            $max_size = 5 * 1024 * 1024; // 5MB
            if ($file['size'] > $max_size) {
                $errors[] = "File {$file_key} is too large. Maximum size is 5MB.";
                continue;
            }

            // Use WordPress media handling
            require_once(ABSPATH . 'wp-admin/includes/file.php');
            require_once(ABSPATH . 'wp-admin/includes/media.php');
            require_once(ABSPATH . 'wp-admin/includes/image.php');

            // Handle the upload
            $upload_overrides = [
                'test_form' => false,
                'unique_filename_callback' => function($dir, $name, $ext) {
                    $user_id = get_current_user_id();
                    $timestamp = time();
                    return "kyc_{$user_id}_{$timestamp}_{$name}";
                }
            ];

            $uploaded_file = wp_handle_upload($file, $upload_overrides);

            if (isset($uploaded_file['error'])) {
                $errors[] = "Error uploading {$file_key}: " . $uploaded_file['error'];
                continue;
            }

            // Create attachment post
            $attachment = [
                'post_mime_type' => $uploaded_file['type'],
                'post_title'     => sanitize_file_name(pathinfo($uploaded_file['file'], PATHINFO_FILENAME)),
                'post_content'   => '',
                'post_status'    => 'inherit'
            ];

            $attachment_id = wp_insert_attachment($attachment, $uploaded_file['file']);

            if (is_wp_error($attachment_id)) {
                $errors[] = "Error creating attachment for {$file_key}: " . $attachment_id->get_error_message();
                continue;
            }

            // Generate attachment metadata
            $attachment_data = wp_generate_attachment_metadata($attachment_id, $uploaded_file['file']);
            wp_update_attachment_metadata($attachment_id, $attachment_data);

            $uploaded_files[$file_key] = [
                'attachment_id' => $attachment_id,
                'url' => $uploaded_file['url'],
                'file' => $uploaded_file['file']
            ];
        }

        if (!empty($errors) && empty($uploaded_files)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'All uploads failed',
                'errors' => $errors
            ], 400);
        }

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Files uploaded successfully',
            'files' => $uploaded_files,
            'errors' => $errors
        ], 200);
    }

    /**
     * Get upload error message
     */
    private function get_upload_error_message($error_code) {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
                return 'The uploaded file exceeds the upload_max_filesize directive in php.ini';
            case UPLOAD_ERR_FORM_SIZE:
                return 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form';
            case UPLOAD_ERR_PARTIAL:
                return 'The uploaded file was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing a temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'A PHP extension stopped the file upload';
            default:
                return 'Unknown upload error';
        }
    }

    /**
     * Submit KYC data with file uploads
     */
    public function submit_kyc($request) {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'User not logged in'
            ], 401);
        }

        $current_user = wp_get_current_user();
        $user_email = $current_user->user_email;

        // Get form data
        $real_name = sanitize_text_field($request->get_param('real_name'));
        $id_number = sanitize_text_field($request->get_param('id_number'));

        if (empty($real_name) || empty($id_number)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Real name and ID number are required'
            ], 400);
        }

        $uploaded_files = [];
        $errors = [];

        // Handle file uploads if present
        if (!empty($_FILES)) {
            foreach ($_FILES as $file_key => $file) {
                if ($file['error'] !== UPLOAD_ERR_OK) {
                    if ($file['error'] !== UPLOAD_ERR_NO_FILE) {
                        $errors[] = "Error uploading {$file_key}: " . $this->get_upload_error_message($file['error']);
                    }
                    continue;
                }

                // Validate file type
                $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (!in_array($file['type'], $allowed_types)) {
                    $errors[] = "Invalid file type for {$file_key}. Only JPEG, PNG, and GIF are allowed.";
                    continue;
                }

                // Validate file size (max 5MB)
                $max_size = 5 * 1024 * 1024; // 5MB
                if ($file['size'] > $max_size) {
                    $errors[] = "File {$file_key} is too large. Maximum size is 5MB.";
                    continue;
                }

                // Use WordPress media handling
                require_once(ABSPATH . 'wp-admin/includes/file.php');
                require_once(ABSPATH . 'wp-admin/includes/media.php');
                require_once(ABSPATH . 'wp-admin/includes/image.php');

                // Handle the upload
                $upload_overrides = [
                    'test_form' => false,
                    'unique_filename_callback' => function($dir, $name, $ext) use ($user_email) {
                        $timestamp = time();
                        $safe_email = sanitize_file_name(str_replace('@', '_at_', $user_email));
                        return "kyc_{$safe_email}_{$timestamp}_{$name}";
                    }
                ];

                $uploaded_file = wp_handle_upload($file, $upload_overrides);

                if (isset($uploaded_file['error'])) {
                    $errors[] = "Error uploading {$file_key}: " . $uploaded_file['error'];
                    continue;
                }

                // Create attachment post
                $attachment = [
                    'post_mime_type' => $uploaded_file['type'],
                    'post_title'     => sanitize_file_name(pathinfo($uploaded_file['file'], PATHINFO_FILENAME)),
                    'post_content'   => '',
                    'post_status'    => 'inherit'
                ];

                $attachment_id = wp_insert_attachment($attachment, $uploaded_file['file']);

                if (is_wp_error($attachment_id)) {
                    $errors[] = "Error creating attachment for {$file_key}: " . $attachment_id->get_error_message();
                    continue;
                }

                // Generate attachment metadata
                $attachment_data = wp_generate_attachment_metadata($attachment_id, $uploaded_file['file']);
                wp_update_attachment_metadata($attachment_id, $attachment_data);

                $uploaded_files[$file_key] = [
                    'attachment_id' => $attachment_id,
                    'url' => $uploaded_file['url'],
                    'file' => $uploaded_file['file']
                ];
            }
        }

        // If there were upload errors and no files were uploaded successfully, return error
        if (!empty($errors) && empty($uploaded_files)) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'File upload failed',
                'errors' => $errors
            ], 400);
        }

        // Now update Supabase with the KYC data
        try {
            $supabase_url = get_option('fil_platform_supabase_url');
            $supabase_service_key = get_option('fil_platform_supabase_service_key');

            if (empty($supabase_url) || empty($supabase_service_key)) {
                throw new Exception('Supabase configuration not found');
            }

            // Get user ID from Supabase using email
            $user_response = wp_remote_get($supabase_url . '/rest/v1/users?email=eq.' . urlencode($user_email), [
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json'
                ]
            ]);

            if (is_wp_error($user_response)) {
                throw new Exception('Failed to connect to Supabase: ' . $user_response->get_error_message());
            }

            $user_data = json_decode(wp_remote_retrieve_body($user_response), true);

            if (empty($user_data)) {
                throw new Exception('User not found in Supabase');
            }

            $supabase_user_id = $user_data[0]['id'];

            // Prepare KYC data for Supabase
            $kyc_data = [
                'user_id' => $supabase_user_id,
                'real_name' => $real_name,
                'id_number' => $id_number,
                'verify_status' => 'pending'
            ];

            // Add image URLs if uploaded
            if (isset($uploaded_files['id_img_front'])) {
                $kyc_data['id_img_front'] = $uploaded_files['id_img_front']['url'];
            }

            if (isset($uploaded_files['id_img_back'])) {
                $kyc_data['id_img_back'] = $uploaded_files['id_img_back']['url'];
            }

            // Update customer_profiles in Supabase using UPSERT
            $supabase_response = wp_remote_post($supabase_url . '/rest/v1/customer_profiles', [
                'method' => 'POST',
                'headers' => [
                    'apikey' => $supabase_service_key,
                    'Authorization' => 'Bearer ' . $supabase_service_key,
                    'Content-Type' => 'application/json',
                    'Prefer' => 'resolution=merge-duplicates'
                ],
                'body' => json_encode($kyc_data)
            ]);

            if (is_wp_error($supabase_response)) {
                throw new Exception('Failed to update Supabase: ' . $supabase_response->get_error_message());
            }

            $response_code = wp_remote_retrieve_response_code($supabase_response);
            if ($response_code >= 400) {
                $error_body = wp_remote_retrieve_body($supabase_response);
                throw new Exception('Supabase error: ' . $error_body);
            }

            return new WP_REST_Response([
                'success' => true,
                'message' => 'KYC submitted successfully',
                'files' => $uploaded_files,
                'errors' => $errors
            ], 200);

        } catch (Exception $e) {
            return new WP_REST_Response([
                'success' => false,
                'message' => 'Failed to submit KYC: ' . $e->getMessage()
            ], 500);
        }
    }
}

new FIL_Platform_API();

// Add a simple settings page for the admin to enter their Supabase credentials.
function fil_platform_add_settings_menu() {
    add_options_page('FIL Platform Settings', 'FIL Platform Settings', 'manage_options', 'fil-platform-settings', 'fil_platform_options_page');
}
add_action('admin_menu', 'fil_platform_add_settings_menu');

function fil_platform_register_settings() {
    register_setting('fil_platform_options', 'fil_platform_supabase_url');
    register_setting('fil_platform_options', 'fil_platform_supabase_anon_key');
    register_setting('fil_platform_options', 'fil_platform_supabase_service_key');
}
add_action('admin_init', 'fil_platform_register_settings');

function fil_platform_options_page() {
    ?>
    <div class="wrap">
        <h1>FIL Platform Settings</h1>
        <form method="post" action="options.php">
            <?php
            settings_fields('fil_platform_options');
            do_settings_sections('fil_platform_options');
            ?>
            <table class="form-table">
                <tr valign="top">
                    <th scope="row">Supabase URL</th>
                    <td><input type="text" name="fil_platform_supabase_url" value="<?php echo esc_attr(get_option('fil_platform_supabase_url')); ?>" size="100" /></td>
                </tr>
                <tr valign="top">
                    <th scope="row">Supabase Anon Key</th>
                    <td><input type="text" name="fil_platform_supabase_anon_key" value="<?php echo esc_attr(get_option('fil_platform_supabase_anon_key')); ?>" size="100"/></td>
                </tr>
                <tr valign="top">
                    <th scope="row">Supabase Service Key</th>
                    <td>
                        <input type="password" name="fil_platform_supabase_service_key" value="<?php echo esc_attr(get_option('fil_platform_supabase_service_key')); ?>" size="100"/>
                        <p class="description">Service role key for server-side operations (required for scraper). Keep this secure!</p>
                    </td>
                </tr>
            </table>
            <?php submit_button(); ?>
        </form>
    </div>
    <?php
}