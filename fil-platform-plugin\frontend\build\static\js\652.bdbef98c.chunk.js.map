{"version": 3, "file": "static/js/652.bdbef98c.chunk.js", "mappings": "2PAMA,MA6JA,EA7JwBA,KACpB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAiBC,IAAsBC,EAAAA,EAAAA,UAAS,KAChDC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAS,KACxCG,EAAiBC,IAAsBJ,EAAAA,EAAAA,UAAS,KAChDK,EAASC,IAAcN,EAAAA,EAAAA,WAAS,IAChCO,EAASC,IAAcR,EAAAA,EAAAA,UAAS,KAChCS,EAAOC,IAAYV,EAAAA,EAAAA,UAAS,IAC7BW,GAAWC,EAAAA,EAAAA,MAyEjB,OACIC,EAAAA,EAAAA,KAACC,EAAAA,EAAS,CAAAC,UACNF,EAAAA,EAAAA,KAACG,EAAAA,EAAG,CAACC,UAAU,yBAAwBF,UACnCF,EAAAA,EAAAA,KAACK,EAAAA,EAAG,CAACC,GAAI,EAAEJ,UACPK,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAAAN,SAAA,EACDF,EAAAA,EAAAA,KAACQ,EAAAA,EAAKC,OAAM,CAAAP,UACRF,EAAAA,EAAAA,KAAA,MAAII,UAAU,OAAMF,SAAEnB,EAAE,8BAE5BwB,EAAAA,EAAAA,MAACC,EAAAA,EAAKE,KAAI,CAAAR,SAAA,CACLN,IAASI,EAAAA,EAAAA,KAACW,EAAAA,EAAK,CAACC,QAAQ,SAAQV,SAAEN,IAClCF,IAAWM,EAAAA,EAAAA,KAACW,EAAAA,EAAK,CAACC,QAAQ,UAASV,SAAER,KAEtCa,EAAAA,EAAAA,MAACM,EAAAA,EAAI,CAACC,SAnFTC,UAMjB,GALAC,EAAEC,iBACFpB,EAAS,IACTF,EAAW,IAGPP,IAAgBE,EAMpB,GAAIF,EAAY8B,OAAS,EACrBrB,EAASd,EAAE,2BADf,CAKAU,GAAW,GAEX,IACI,MAAM0B,GAAWC,EAAAA,EAAAA,KACjB,IAAKD,EACD,MAAM,IAAIE,MAAMtC,EAAE,8BAItB,MAAQuC,MAAM,KAAEC,GAAQ3B,MAAO4B,SAAoBL,EAASM,KAAKC,UACjE,GAAIF,IAAcD,EACd,MAAM,IAAIF,MAAMtC,EAAE,2BAKtB,MAAQa,MAAO+B,SAAsBR,EAASM,KAAKG,mBAAmB,CAClEC,MAAON,EAAKM,MACZC,SAAU7C,IAGd,GAAI0C,EACA,MAAM,IAAIN,MAAMtC,EAAE,+BAItB,MAAQa,MAAOmC,SAAsBZ,EAASM,KAAKO,WAAW,CAC1DF,SAAU1C,IAGd,GAAI2C,EACA,MAAMA,EAGVpC,EAAWZ,EAAE,kCAGbG,EAAmB,IACnBG,EAAe,IACfE,EAAmB,IAGnB0C,WAAW,KACPnC,EAAS,QACV,IAEP,CAAE,MAAOoC,GACLC,QAAQvC,MAAM,yBAA0BsC,GACxCrC,EAASqC,EAAIxC,SAAWX,EAAE,0BAC9B,CAEAU,GAAW,EArDX,MARII,EAASd,EAAE,4BA4EkCmB,SAAA,EACzBK,EAAAA,EAAAA,MAACM,EAAAA,EAAKuB,MAAK,CAAChC,UAAU,OAAMF,SAAA,EACxBF,EAAAA,EAAAA,KAACa,EAAAA,EAAKwB,MAAK,CAAAnC,SAAEnB,EAAE,uBACfiB,EAAAA,EAAAA,KAACa,EAAAA,EAAKyB,QAAO,CACTC,KAAK,WACLC,MAAOvD,EACPwD,SAAWzB,GAAM9B,EAAmB8B,EAAE0B,OAAOF,OAC7CG,UAAQ,EACRC,YAAa7D,EAAE,gCAIvBwB,EAAAA,EAAAA,MAACM,EAAAA,EAAKuB,MAAK,CAAChC,UAAU,OAAMF,SAAA,EACxBF,EAAAA,EAAAA,KAACa,EAAAA,EAAKwB,MAAK,CAAAnC,SAAEnB,EAAE,mBACfiB,EAAAA,EAAAA,KAACa,EAAAA,EAAKyB,QAAO,CACTC,KAAK,WACLC,MAAOpD,EACPqD,SAAWzB,GAAM3B,EAAe2B,EAAE0B,OAAOF,OACzCG,UAAQ,EACRE,UAAW,EACXD,YAAa7D,EAAE,yBAEnBiB,EAAAA,EAAAA,KAACa,EAAAA,EAAKiC,KAAI,CAAC1C,UAAU,aAAYF,SAC5BnB,EAAE,+BAIXwB,EAAAA,EAAAA,MAACM,EAAAA,EAAKuB,MAAK,CAAChC,UAAU,OAAMF,SAAA,EACxBF,EAAAA,EAAAA,KAACa,EAAAA,EAAKwB,MAAK,CAAAnC,SAAEnB,EAAE,2BACfiB,EAAAA,EAAAA,KAACa,EAAAA,EAAKyB,QAAO,CACTC,KAAK,WACLC,MAAOlD,EACPmD,SAAWzB,GAAMzB,EAAmByB,EAAE0B,OAAOF,OAC7CG,UAAQ,EACRE,UAAW,EACXD,YAAa7D,EAAE,8BAIvBwB,EAAAA,EAAAA,MAAA,OAAKH,UAAU,eAAcF,SAAA,EACzBF,EAAAA,EAAAA,KAAC+C,EAAAA,EAAM,CACHnC,QAAQ,UACR2B,KAAK,SACLS,SAAUxD,EAAQU,SAEPnB,EAAVS,EAAY,WAAgB,sBAEjCQ,EAAAA,EAAAA,KAAC+C,EAAAA,EAAM,CACHnC,QAAQ,YACRqC,QAASA,IAAMnD,EAAS,OACxBkD,SAAUxD,EAAQU,SAEjBnB,EAAE,8B,sFCjJ3C,MAAMoB,EAAmB+C,EAAAA,WAAiB,CAAAC,EAMvCC,KAAQ,IANgC,SACzCC,EAAQ,UACRjD,EAEAkD,GAAIC,EAAY,SACbC,GACJL,EACC,MAAMM,GAAoBC,EAAAA,EAAAA,IAAmBL,EAAU,OACjDM,GAAcC,EAAAA,EAAAA,MACdC,GAAgBC,EAAAA,EAAAA,MAChBC,EAAa,GAAGN,SAChBO,EAAU,GAehB,OAdAL,EAAYM,QAAQC,IAClB,MAAMC,EAAYX,EAAMU,GAExB,IAAIE,SADGZ,EAAMU,GAEI,MAAbC,GAA0C,kBAAdA,IAE5BC,QACED,GAEJC,EAAOD,EAET,MAAME,EAAQH,IAAaL,EAAgB,IAAIK,IAAa,GAChD,MAARE,GAAcJ,EAAQM,KAAK,GAAGP,IAAaM,KAASD,QAEtCpE,EAAAA,EAAAA,KAAKuD,EAAW,CAClCH,IAAKA,KACFI,EACHpD,UAAWmE,IAAWnE,EAAWqD,KAAsBO,OAG3D7D,EAAIqE,YAAc,MAClB,S,oHChCA,MAAMC,GAAgBC,EAAAA,EAAAA,GAAiB,MACvCD,EAAcD,YAAc,gBAC5B,MAAMG,EAA4BzB,EAAAA,WAAiB,CAAAC,EAKhDC,KAAQ,IALyC,UAClDhD,EAAS,SACTiD,EACAC,GAAIC,EAAYkB,KACbjB,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,kBACpBrD,EAAAA,EAAAA,KAAKuD,EAAW,CAClCH,IAAKA,EACLhD,UAAWmE,IAAWnE,EAAWiD,MAC9BG,MAGPmB,EAAaH,YAAc,eAC3B,U,cChBA,MAAMI,EAAyB1B,EAAAA,WAAiB,CAAAC,EAK7CC,KAAQ,IALsC,UAC/ChD,EAAS,SACTiD,EACAC,GAAIC,EAAYsB,EAAAA,KACbrB,GACJL,EAEC,OADAE,GAAWK,EAAAA,EAAAA,IAAmBL,EAAU,eACpBrD,EAAAA,EAAAA,KAAKuD,EAAW,CAClCH,IAAKA,EACLhD,UAAWmE,IAAWnE,EAAWiD,MAC9BG,MAGPoB,EAAUJ,YAAc,YACxB,U,wBCRA,MAAM7D,EAAqBuC,EAAAA,WAAiB,CAAC4B,EAAmB1B,KAC9D,MAAM,SACJC,EAAQ,KACR0B,GAAO,EAAI,WACXC,EAAa,cAAa,aAC1BC,EAAY,UACZ7E,EAAS,SACTF,EAAQ,QACRU,EAAU,UAAS,QACnBsE,EAAO,YACPC,EAAW,WACXC,EAAaC,EAAAA,KACV7B,IACD8B,EAAAA,EAAAA,IAAgBR,EAAmB,CACrCC,KAAM,YAEFQ,GAAS7B,EAAAA,EAAAA,IAAmBL,EAAU,SACtCmC,GAAcC,EAAAA,EAAAA,GAAiBzE,IAC/BkE,GACFA,GAAQ,EAAOlE,KAGb0E,GAA4B,IAAfN,EAAsBC,EAAAA,EAAOD,EAC1CO,GAAqBpF,EAAAA,EAAAA,MAAM,MAAO,CACtCqF,KAAM,WACDF,OAAqBG,EAARrC,EAClBJ,IAAKA,EACLhD,UAAWmE,IAAWnE,EAAWmF,EAAQ3E,GAAW,GAAG2E,KAAU3E,IAAWuE,GAAe,GAAGI,iBAC9FrF,SAAU,CAACiF,IAA4BnF,EAAAA,EAAAA,KAAK8F,EAAAA,EAAa,CACvD7C,QAASuC,EACT,aAAcR,EACdpE,QAASqE,IACP/E,KAEN,OAAKwF,GACe1F,EAAAA,EAAAA,KAAK0F,EAAY,CACnCK,eAAe,KACZvC,EACHJ,SAAKyC,EACLG,GAAIjB,EACJ7E,SAAUyF,IANYZ,EAAOY,EAAQ,OASzChF,EAAM6D,YAAc,QACpB,QAAeyB,OAAOC,OAAOvF,EAAO,CAClCwF,KAAMvB,EACNwB,QAASzB,G", "sources": ["pages/customer/ChangeLoginPass.js", "../node_modules/react-bootstrap/esm/Row.js", "../node_modules/react-bootstrap/esm/AlertHeading.js", "../node_modules/react-bootstrap/esm/AlertLink.js", "../node_modules/react-bootstrap/esm/Alert.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Container, Row, Col, Card, Form, Button, Alert } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { getSupabase } from '../../supabaseClient';\nimport { useNavigate } from 'react-router-dom';\n\nconst ChangeLoginPass = () => {\n    const { t } = useTranslation();\n    const [currentPassword, setCurrentPassword] = useState('');\n    const [newPassword, setNewPassword] = useState('');\n    const [confirmPassword, setConfirmPassword] = useState('');\n    const [loading, setLoading] = useState(false);\n    const [message, setMessage] = useState('');\n    const [error, setError] = useState('');\n    const navigate = useNavigate();\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setError('');\n        setMessage('');\n\n        // 验证新密码和确认密码是否一致\n        if (newPassword !== confirmPassword) {\n            setError(t('passwords_do_not_match'));\n            return;\n        }\n\n        // 验证新密码长度\n        if (newPassword.length < 6) {\n            setError(t('password_too_short'));\n            return;\n        }\n\n        setLoading(true);\n\n        try {\n            const supabase = getSupabase();\n            if (!supabase) {\n                throw new Error(t('backend_connection_failed'));\n            }\n\n            // 获取当前用户\n            const { data: { user }, error: userError } = await supabase.auth.getUser();\n            if (userError || !user) {\n                throw new Error(t('user_not_authenticated'));\n            }\n\n            // 首先验证当前密码是否正确\n            // 通过重新登录来验证当前密码\n            const { error: signInError } = await supabase.auth.signInWithPassword({\n                email: user.email,\n                password: currentPassword,\n            });\n\n            if (signInError) {\n                throw new Error(t('current_password_incorrect'));\n            }\n\n            // 更新密码\n            const { error: updateError } = await supabase.auth.updateUser({\n                password: newPassword\n            });\n\n            if (updateError) {\n                throw updateError;\n            }\n\n            setMessage(t('password_updated_successfully'));\n            \n            // 清空表单\n            setCurrentPassword('');\n            setNewPassword('');\n            setConfirmPassword('');\n\n            // 3秒后跳转回我的账户页面\n            setTimeout(() => {\n                navigate('/my');\n            }, 3000);\n\n        } catch (err) {\n            console.error('Password update error:', err);\n            setError(err.message || t('password_update_failed'));\n        }\n\n        setLoading(false);\n    };\n\n    return (\n        <Container>\n            <Row className=\"justify-content-center\">\n                <Col md={6}>\n                    <Card>\n                        <Card.Header>\n                            <h4 className=\"mb-0\">{t('change_login_password')}</h4>\n                        </Card.Header>\n                        <Card.Body>\n                            {error && <Alert variant=\"danger\">{error}</Alert>}\n                            {message && <Alert variant=\"success\">{message}</Alert>}\n                            \n                            <Form onSubmit={handleSubmit}>\n                                <Form.Group className=\"mb-3\">\n                                    <Form.Label>{t('current_password')}</Form.Label>\n                                    <Form.Control\n                                        type=\"password\"\n                                        value={currentPassword}\n                                        onChange={(e) => setCurrentPassword(e.target.value)}\n                                        required\n                                        placeholder={t('enter_current_password')}\n                                    />\n                                </Form.Group>\n\n                                <Form.Group className=\"mb-3\">\n                                    <Form.Label>{t('new_password')}</Form.Label>\n                                    <Form.Control\n                                        type=\"password\"\n                                        value={newPassword}\n                                        onChange={(e) => setNewPassword(e.target.value)}\n                                        required\n                                        minLength={6}\n                                        placeholder={t('enter_new_password')}\n                                    />\n                                    <Form.Text className=\"text-muted\">\n                                        {t('password_requirements')}\n                                    </Form.Text>\n                                </Form.Group>\n\n                                <Form.Group className=\"mb-3\">\n                                    <Form.Label>{t('confirm_new_password')}</Form.Label>\n                                    <Form.Control\n                                        type=\"password\"\n                                        value={confirmPassword}\n                                        onChange={(e) => setConfirmPassword(e.target.value)}\n                                        required\n                                        minLength={6}\n                                        placeholder={t('confirm_new_password')}\n                                    />\n                                </Form.Group>\n\n                                <div className=\"d-grid gap-2\">\n                                    <Button \n                                        variant=\"primary\" \n                                        type=\"submit\" \n                                        disabled={loading}\n                                    >\n                                        {loading ? t('updating') : t('update_password')}\n                                    </Button>\n                                    <Button \n                                        variant=\"secondary\" \n                                        onClick={() => navigate('/my')}\n                                        disabled={loading}\n                                    >\n                                        {t('cancel')}\n                                    </Button>\n                                </div>\n                            </Form>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default ChangeLoginPass;\n", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Row = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'row');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const sizePrefix = `${decoratedBsPrefix}-cols`;\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let cols;\n    if (propValue != null && typeof propValue === 'object') {\n      ({\n        cols\n      } = propValue);\n    } else {\n      cols = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (cols != null) classes.push(`${sizePrefix}${infix}-${cols}`);\n  });\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, decoratedBsPrefix, ...classes)\n  });\n});\nRow.displayName = 'Row';\nexport default Row;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nDivStyledAsH4.displayName = 'DivStyledAsH4';\nconst AlertHeading = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-heading');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertHeading.displayName = 'AlertHeading';\nexport default AlertHeading;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AlertLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = Anchor,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'alert-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nAlertLink.displayName = 'AlertLink';\nexport default AlertLink;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AlertHeading from './AlertHeading';\nimport AlertLink from './AlertLink';\nimport Fade from './Fade';\nimport CloseButton from './CloseButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Alert = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    bsPrefix,\n    show = true,\n    closeLabel = 'Close alert',\n    closeVariant,\n    className,\n    children,\n    variant = 'primary',\n    onClose,\n    dismissible,\n    transition = Fade,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    show: 'onClose'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'alert');\n  const handleClose = useEventCallback(e => {\n    if (onClose) {\n      onClose(false, e);\n    }\n  });\n  const Transition = transition === true ? Fade : transition;\n  const alert = /*#__PURE__*/_jsxs(\"div\", {\n    role: \"alert\",\n    ...(!Transition ? props : undefined),\n    ref: ref,\n    className: classNames(className, prefix, variant && `${prefix}-${variant}`, dismissible && `${prefix}-dismissible`),\n    children: [dismissible && /*#__PURE__*/_jsx(CloseButton, {\n      onClick: handleClose,\n      \"aria-label\": closeLabel,\n      variant: closeVariant\n    }), children]\n  });\n  if (!Transition) return show ? alert : null;\n  return /*#__PURE__*/_jsx(Transition, {\n    unmountOnExit: true,\n    ...props,\n    ref: undefined,\n    in: show,\n    children: alert\n  });\n});\nAlert.displayName = 'Alert';\nexport default Object.assign(Alert, {\n  Link: AlertLink,\n  Heading: AlertHeading\n});"], "names": ["ChangeLoginPass", "t", "useTranslation", "currentPassword", "setCurrentPassword", "useState", "newPassword", "setNewPassword", "confirmPassword", "setConfirmPassword", "loading", "setLoading", "message", "setMessage", "error", "setError", "navigate", "useNavigate", "_jsx", "Container", "children", "Row", "className", "Col", "md", "_jsxs", "Card", "Header", "Body", "<PERSON><PERSON>", "variant", "Form", "onSubmit", "async", "e", "preventDefault", "length", "supabase", "getSupabase", "Error", "data", "user", "userError", "auth", "getUser", "signInError", "signInWithPassword", "email", "password", "updateError", "updateUser", "setTimeout", "err", "console", "Group", "Label", "Control", "type", "value", "onChange", "target", "required", "placeholder", "<PERSON><PERSON><PERSON><PERSON>", "Text", "<PERSON><PERSON>", "disabled", "onClick", "React", "_ref", "ref", "bsPrefix", "as", "Component", "props", "decoratedBsPrefix", "useBootstrapPrefix", "breakpoints", "useBootstrapBreakpoints", "minBreakpoint", "useBootstrapMinBreakpoint", "sizePrefix", "classes", "for<PERSON>ach", "brkPoint", "propValue", "cols", "infix", "push", "classNames", "displayName", "DivStyledAsH4", "divWithClassName", "AlertHeading", "AlertLink", "<PERSON><PERSON>", "uncontrolledProps", "show", "<PERSON><PERSON><PERSON><PERSON>", "closeVariant", "onClose", "dismissible", "transition", "Fade", "useUncontrolled", "prefix", "handleClose", "useEventCallback", "Transition", "alert", "role", "undefined", "CloseButton", "unmountOnExit", "in", "Object", "assign", "Link", "Heading"], "sourceRoot": ""}