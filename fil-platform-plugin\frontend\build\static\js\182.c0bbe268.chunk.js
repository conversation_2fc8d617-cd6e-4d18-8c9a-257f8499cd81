"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[182],{1072:(e,s,r)=>{r.d(s,{A:()=>d});var a=r(8139),t=r.n(a),l=r(5043),i=r(7852),n=r(579);const o=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:l="div",...o}=e;const d=(0,i.oU)(r,"row"),c=(0,i.gy)(),x=(0,i.Jm)(),h=`${d}-cols`,m=[];return c.forEach(e=>{const s=o[e];let r;delete o[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==x?`-${e}`:"";null!=r&&m.push(`${h}${a}-${r}`)}),(0,n.jsx)(l,{ref:s,...o,className:t()(a,d,...m)})});o.displayName="Row";const d=o},1182:(e,s,r)=>{r.r(s),r.d(s,{default:()=>u});var a=r(5043),t=r(4117),l=r(8628),i=r(7417),n=r(3519),o=r(1072),d=r(8602),c=r(1719),x=r(4282),h=r(4196),m=r(4312),f=r(579);const j=e=>{let{title:s,value:r,unit:a,loading:t,icon:n}=e;return(0,f.jsx)(l.A,{className:"mb-3 h-100",children:(0,f.jsx)(l.A.Body,{className:"d-flex flex-column justify-content-between",children:(0,f.jsxs)("div",{className:"d-flex justify-content-between align-items-start",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)(l.A.Title,{className:"h6",children:s}),t?(0,f.jsxs)("div",{className:"d-flex align-items-center",children:[(0,f.jsx)(i.A,{animation:"border",size:"sm",className:"me-2"}),(0,f.jsx)("span",{children:"Loading..."})]}):(0,f.jsxs)("div",{children:[(0,f.jsx)("h4",{className:"mb-0",children:r}),a&&(0,f.jsx)("small",{className:"opacity-75",children:a})]})]}),n&&(0,f.jsx)("div",{className:"fs-2 opacity-50",children:n})]})})})},u=()=>{const{t:e}=(0,t.Bd)(),[s,r]=(0,a.useState)(!0),[u,g]=(0,a.useState)(null),[v,p]=(0,a.useState)(null),[b,N]=(0,a.useState)([]),[_,w]=(0,a.useState)(!1),A=async()=>{try{r(!0);const e=window.location.origin+"/wp-json/fil-platform/v1/test";console.log("Testing API connection:",e);const s=await fetch(e,{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});if(console.log("Test response status:",s.status),s.ok){const e=await s.json();console.log("Test API Response:",e)}else console.error("Test API failed:",s.status);const a=window.location.origin+"/wp-json/fil-platform/v1/filfox-realtime";console.log("Fetching from URL:",a);const t=await fetch(a,{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});if(console.log("Response status:",t.status),!t.ok){const e=await t.text();throw console.error("Error response:",e),new Error(`HTTP error! status: ${t.status} - ${e}`)}const l=await t.json();console.log("API Response:",l),l.success?(p(l.data),g(null)):g(l.message||"Failed to fetch real-time data"),await y()}catch(u){console.error("Error fetching real-time stats:",u),g("Failed to load real-time network statistics: "+u.message)}finally{r(!1),w(!1)}},y=async()=>{const e=(0,m.b)();if(e)try{const{data:{user:s}}=await e.auth.getUser();if(!s)return;const{data:r,error:a}=await e.from("network_stats").select("stat_date, fil_per_tib").order("stat_date",{ascending:!1}).limit(30);a?console.error("Error fetching historical stats:",a):N(r.reverse())}catch(u){console.error("Error fetching historical data:",u)}};(0,a.useEffect)(()=>{A()},[]);const $=e=>null===e||void 0===e?"N/A":new Intl.NumberFormat("en-US",{minimumFractionDigits:0,maximumFractionDigits:4}).format(e);return u?(0,f.jsx)(n.A,{fluid:!0,children:(0,f.jsx)(o.A,{className:"mb-3",children:(0,f.jsxs)(d.A,{children:[(0,f.jsx)("h2",{children:e("filfox_network_stats")}),(0,f.jsx)(c.A,{children:u})]})})}):(0,f.jsxs)(n.A,{fluid:!0,children:[(0,f.jsx)(o.A,{className:"mb-3",children:(0,f.jsx)(d.A,{children:(0,f.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,f.jsx)("h2",{children:e("filfox_network_stats")}),(0,f.jsx)(x.A,{onClick:()=>{w(!0),A()},disabled:_,children:_?(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(i.A,{animation:"border",size:"sm",className:"me-2"}),e("refreshing")]}):e("refresh")})]})})}),(0,f.jsxs)(o.A,{className:"mb-4",children:[(0,f.jsx)(d.A,{md:3,children:(0,f.jsx)(j,{title:e("block_height"),value:$(null===v||void 0===v?void 0:v.block_height),loading:s,icon:"\ud83d\udd17"})}),(0,f.jsx)(d.A,{md:3,children:(0,f.jsx)(j,{title:e("network_storage_power"),value:$(null===v||void 0===v?void 0:v.network_storage_power),unit:"EiB",loading:s,icon:"\ud83d\udcbe"})}),(0,f.jsx)(d.A,{md:3,children:(0,f.jsx)(j,{title:e("active_miners"),value:$(null===v||void 0===v?void 0:v.active_miners),loading:s,icon:"\u26cf\ufe0f"})}),(0,f.jsx)(d.A,{md:3,children:(0,f.jsx)(j,{title:e("block_reward"),value:$(null===v||void 0===v?void 0:v.block_reward),unit:"FIL",loading:s,icon:"\ud83c\udf81"})})]}),(0,f.jsxs)(o.A,{className:"mb-4",children:[(0,f.jsx)(d.A,{md:3,children:(0,f.jsx)(j,{title:e("mining_reward_24h"),value:$(null===v||void 0===v?void 0:v.mining_reward),unit:"FIL/TiB",loading:s,icon:"\u26a1"})}),(0,f.jsx)(d.A,{md:3,children:(0,f.jsx)(j,{title:e("fil_production_24h"),value:$(null===v||void 0===v?void 0:v.fil_production_24h),unit:"FIL",loading:s,icon:"\ud83c\udfed"})}),(0,f.jsx)(d.A,{md:3,children:(0,f.jsx)(j,{title:e("total_pledge_collateral"),value:$(null===v||void 0===v?void 0:v.total_pledge_collateral),unit:"FIL",loading:s,icon:"\ud83d\udd12"})}),(0,f.jsx)(d.A,{md:3,children:(0,f.jsx)(j,{title:e("messages_24h"),value:$(null===v||void 0===v?void 0:v.messages_24h),loading:s,icon:"\ud83d\udce8"})})]}),(0,f.jsxs)(o.A,{className:"mb-4",children:[(0,f.jsx)(d.A,{md:6,children:(0,f.jsx)(j,{title:e("sector_initial_pledge"),value:$(null===v||void 0===v?void 0:v.sector_initial_pledge),unit:"FIL/32GiB",loading:s,icon:"\ud83d\udd10"})}),(0,f.jsx)(d.A,{md:6,children:(0,f.jsx)(j,{title:e("latest_block"),value:(null===v||void 0===v?void 0:v.latest_block)||"N/A",loading:s,icon:"\u23f0"})})]}),(0,f.jsx)(o.A,{children:(0,f.jsx)(d.A,{children:(0,f.jsx)(l.A,{children:(0,f.jsxs)(l.A.Body,{children:[(0,f.jsx)(l.A.Title,{children:e("current_network_summary")}),s?(0,f.jsxs)("div",{className:"text-center",children:[(0,f.jsx)(i.A,{animation:"border"}),(0,f.jsx)("p",{className:"mt-2",children:e("loading")})]}):v?(0,f.jsx)(h.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:(0,f.jsxs)("tbody",{children:[(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:(0,f.jsx)("strong",{children:e("block_height")})}),(0,f.jsx)("td",{children:$(v.block_height)}),(0,f.jsx)("td",{children:(0,f.jsx)("strong",{children:e("network_storage_power")})}),(0,f.jsxs)("td",{children:[$(v.network_storage_power)," EiB"]})]}),(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:(0,f.jsx)("strong",{children:e("active_miners")})}),(0,f.jsx)("td",{children:$(v.active_miners)}),(0,f.jsx)("td",{children:(0,f.jsx)("strong",{children:e("block_reward")})}),(0,f.jsxs)("td",{children:[$(v.block_reward)," FIL"]})]}),(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:(0,f.jsx)("strong",{children:e("mining_reward_24h")})}),(0,f.jsxs)("td",{children:[$(v.mining_reward)," FIL/TiB"]}),(0,f.jsx)("td",{children:(0,f.jsx)("strong",{children:e("fil_production_24h")})}),(0,f.jsxs)("td",{children:[$(v.fil_production_24h)," FIL"]})]}),(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:(0,f.jsx)("strong",{children:e("total_pledge_collateral")})}),(0,f.jsxs)("td",{children:[$(v.total_pledge_collateral)," FIL"]}),(0,f.jsx)("td",{children:(0,f.jsx)("strong",{children:e("messages_24h")})}),(0,f.jsx)("td",{children:$(v.messages_24h)})]}),(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:(0,f.jsx)("strong",{children:e("sector_initial_pledge")})}),(0,f.jsxs)("td",{children:[$(v.sector_initial_pledge)," FIL/32GiB"]}),(0,f.jsx)("td",{children:(0,f.jsx)("strong",{children:e("latest_block")})}),(0,f.jsx)("td",{children:v.latest_block||"N/A"})]}),(0,f.jsxs)("tr",{children:[(0,f.jsx)("td",{children:(0,f.jsx)("strong",{children:e("last_updated")})}),(0,f.jsx)("td",{colSpan:"3",children:v.scraped_at?new Date(v.scraped_at).toLocaleString():"N/A"})]})]})}):(0,f.jsx)("p",{children:e("no_data_available")})]})})})})]})}},1719:(e,s,r)=>{r.d(s,{A:()=>b});var a=r(8139),t=r.n(a),l=r(5043),i=r(1969),n=r(6618),o=r(7852),d=r(4488),c=r(579);const x=(0,d.A)("h4");x.displayName="DivStyledAsH4";const h=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=x,...i}=e;return a=(0,o.oU)(a,"alert-heading"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...i})});h.displayName="AlertHeading";const m=h;var f=r(7071);const j=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=f.A,...i}=e;return a=(0,o.oU)(a,"alert-link"),(0,c.jsx)(l,{ref:s,className:t()(r,a),...i})});j.displayName="AlertLink";const u=j;var g=r(8072),v=r(5632);const p=l.forwardRef((e,s)=>{const{bsPrefix:r,show:a=!0,closeLabel:l="Close alert",closeVariant:d,className:x,children:h,variant:m="primary",onClose:f,dismissible:j,transition:u=g.A,...p}=(0,i.Zw)(e,{show:"onClose"}),b=(0,o.oU)(r,"alert"),N=(0,n.A)(e=>{f&&f(!1,e)}),_=!0===u?g.A:u,w=(0,c.jsxs)("div",{role:"alert",..._?void 0:p,ref:s,className:t()(x,b,m&&`${b}-${m}`,j&&`${b}-dismissible`),children:[j&&(0,c.jsx)(v.A,{onClick:N,"aria-label":l,variant:d}),h]});return _?(0,c.jsx)(_,{unmountOnExit:!0,...p,ref:void 0,in:a,children:w}):a?w:null});p.displayName="Alert";const b=Object.assign(p,{Link:u,Heading:m})},4196:(e,s,r)=>{r.d(s,{A:()=>d});var a=r(8139),t=r.n(a),l=r(5043),i=r(7852),n=r(579);const o=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:l,bordered:o,borderless:d,hover:c,size:x,variant:h,responsive:m,...f}=e;const j=(0,i.oU)(r,"table"),u=t()(a,j,h&&`${j}-${h}`,x&&`${j}-${x}`,l&&`${j}-${"string"===typeof l?`striped-${l}`:"striped"}`,o&&`${j}-bordered`,d&&`${j}-borderless`,c&&`${j}-hover`),g=(0,n.jsx)("table",{...f,className:u,ref:s});if(m){let e=`${j}-responsive`;return"string"===typeof m&&(e=`${e}-${m}`),(0,n.jsx)("div",{className:e,children:g})}return g});o.displayName="Table";const d=o},7417:(e,s,r)=>{r.d(s,{A:()=>d});var a=r(8139),t=r.n(a),l=r(5043),i=r(7852),n=r(579);const o=l.forwardRef((e,s)=>{let{bsPrefix:r,variant:a,animation:l="border",size:o,as:d="div",className:c,...x}=e;r=(0,i.oU)(r,"spinner");const h=`${r}-${l}`;return(0,n.jsx)(d,{ref:s,...x,className:t()(c,h,o&&`${h}-${o}`,a&&`text-${a}`)})});o.displayName="Spinner";const d=o},8602:(e,s,r)=>{r.d(s,{A:()=>d});var a=r(8139),t=r.n(a),l=r(5043),i=r(7852),n=r(579);const o=l.forwardRef((e,s)=>{const[{className:r,...a},{as:l="div",bsPrefix:o,spans:d}]=function(e){let{as:s,bsPrefix:r,className:a,...l}=e;r=(0,i.oU)(r,"col");const n=(0,i.gy)(),o=(0,i.Jm)(),d=[],c=[];return n.forEach(e=>{const s=l[e];let a,t,i;delete l[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:i}=s):a=s;const n=e!==o?`-${e}`:"";a&&d.push(!0===a?`${r}${n}`:`${r}${n}-${a}`),null!=i&&c.push(`order${n}-${i}`),null!=t&&c.push(`offset${n}-${t}`)}),[{...l,className:t()(a,...d,...c)},{as:s,bsPrefix:r,spans:d}]}(e);return(0,n.jsx)(l,{...a,ref:s,className:t()(r,!d.length&&o)})});o.displayName="Col";const d=o},8628:(e,s,r)=>{r.d(s,{A:()=>U});var a=r(8139),t=r.n(a),l=r(5043),i=r(7852),n=r(579);const o=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...o}=e;return a=(0,i.oU)(a,"card-body"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});o.displayName="CardBody";const d=o,c=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...o}=e;return a=(0,i.oU)(a,"card-footer"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});c.displayName="CardFooter";const x=c;var h=r(1778);const m=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:o="div",...d}=e;const c=(0,i.oU)(r,"card-header"),x=(0,l.useMemo)(()=>({cardHeaderBsPrefix:c}),[c]);return(0,n.jsx)(h.A.Provider,{value:x,children:(0,n.jsx)(o,{ref:s,...d,className:t()(a,c)})})});m.displayName="CardHeader";const f=m,j=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:l,as:o="img",...d}=e;const c=(0,i.oU)(r,"card-img");return(0,n.jsx)(o,{ref:s,className:t()(l?`${c}-${l}`:c,a),...d})});j.displayName="CardImg";const u=j,g=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="div",...o}=e;return a=(0,i.oU)(a,"card-img-overlay"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});g.displayName="CardImgOverlay";const v=g,p=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="a",...o}=e;return a=(0,i.oU)(a,"card-link"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});p.displayName="CardLink";const b=p;var N=r(4488);const _=(0,N.A)("h6"),w=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=_,...o}=e;return a=(0,i.oU)(a,"card-subtitle"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});w.displayName="CardSubtitle";const A=w,y=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l="p",...o}=e;return a=(0,i.oU)(a,"card-text"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});y.displayName="CardText";const $=y,k=(0,N.A)("h5"),P=l.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:l=k,...o}=e;return a=(0,i.oU)(a,"card-title"),(0,n.jsx)(l,{ref:s,className:t()(r,a),...o})});P.displayName="CardTitle";const R=P,C=l.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:l,text:o,border:c,body:x=!1,children:h,as:m="div",...f}=e;const j=(0,i.oU)(r,"card");return(0,n.jsx)(m,{ref:s,...f,className:t()(a,j,l&&`bg-${l}`,o&&`text-${o}`,c&&`border-${c}`),children:x?(0,n.jsx)(d,{children:h}):h})});C.displayName="Card";const U=Object.assign(C,{Img:u,Title:R,Subtitle:A,Body:d,Link:b,Text:$,Header:f,Footer:x,ImgOverlay:v})}}]);
//# sourceMappingURL=182.c0bbe268.chunk.js.map