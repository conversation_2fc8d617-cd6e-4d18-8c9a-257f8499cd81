"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[652],{652:(e,s,r)=>{r.r(s),r.d(s,{default:()=>w});var a=r(5043),t=r(3519),n=r(1072),o=r(8602),l=r(8628),d=r(1719),i=r(3722),c=r(4282),u=r(4117),h=r(4312),p=r(1283),m=r(579);const w=()=>{const{t:e}=(0,u.Bd)(),[s,r]=(0,a.useState)(""),[w,f]=(0,a.useState)(""),[x,A]=(0,a.useState)(""),[j,_]=(0,a.useState)(!1),[b,v]=(0,a.useState)(""),[g,y]=(0,a.useState)(""),N=(0,p.Zp)();return(0,m.jsx)(t.A,{children:(0,m.jsx)(n.A,{className:"justify-content-center",children:(0,m.jsx)(o.A,{md:6,children:(0,m.jsxs)(l.A,{children:[(0,m.jsx)(l.A.Header,{children:(0,m.jsx)("h4",{className:"mb-0",children:e("change_login_password")})}),(0,m.jsxs)(l.A.Body,{children:[g&&(0,m.jsx)(d.A,{variant:"danger",children:g}),b&&(0,m.jsx)(d.A,{variant:"success",children:b}),(0,m.jsxs)(i.A,{onSubmit:async a=>{if(a.preventDefault(),y(""),v(""),w===x)if(w.length<6)y(e("password_too_short"));else{_(!0);try{const a=(0,h.b)();if(!a)throw new Error(e("backend_connection_failed"));const{data:{user:t},error:n}=await a.auth.getUser();if(n||!t)throw new Error(e("user_not_authenticated"));const{error:o}=await a.auth.signInWithPassword({email:t.email,password:s});if(o)throw new Error(e("current_password_incorrect"));const{error:l}=await a.auth.updateUser({password:w});if(l)throw l;v(e("password_updated_successfully")),r(""),f(""),A(""),setTimeout(()=>{N("/my")},3e3)}catch(t){console.error("Password update error:",t),y(t.message||e("password_update_failed"))}_(!1)}else y(e("passwords_do_not_match"))},children:[(0,m.jsxs)(i.A.Group,{className:"mb-3",children:[(0,m.jsx)(i.A.Label,{children:e("current_password")}),(0,m.jsx)(i.A.Control,{type:"password",value:s,onChange:e=>r(e.target.value),required:!0,placeholder:e("enter_current_password")})]}),(0,m.jsxs)(i.A.Group,{className:"mb-3",children:[(0,m.jsx)(i.A.Label,{children:e("new_password")}),(0,m.jsx)(i.A.Control,{type:"password",value:w,onChange:e=>f(e.target.value),required:!0,minLength:6,placeholder:e("enter_new_password")}),(0,m.jsx)(i.A.Text,{className:"text-muted",children:e("password_requirements")})]}),(0,m.jsxs)(i.A.Group,{className:"mb-3",children:[(0,m.jsx)(i.A.Label,{children:e("confirm_new_password")}),(0,m.jsx)(i.A.Control,{type:"password",value:x,onChange:e=>A(e.target.value),required:!0,minLength:6,placeholder:e("confirm_new_password")})]}),(0,m.jsxs)("div",{className:"d-grid gap-2",children:[(0,m.jsx)(c.A,{variant:"primary",type:"submit",disabled:j,children:e(j?"updating":"update_password")}),(0,m.jsx)(c.A,{variant:"secondary",onClick:()=>N("/my"),disabled:j,children:e("cancel")})]})]})]})]})})})})}},1072:(e,s,r)=>{r.d(s,{A:()=>i});var a=r(8139),t=r.n(a),n=r(5043),o=r(7852),l=r(579);const d=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:n="div",...d}=e;const i=(0,o.oU)(r,"row"),c=(0,o.gy)(),u=(0,o.Jm)(),h=`${i}-cols`,p=[];return c.forEach(e=>{const s=d[e];let r;delete d[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==u?`-${e}`:"";null!=r&&p.push(`${h}${a}-${r}`)}),(0,l.jsx)(n,{ref:s,...d,className:t()(a,i,...p)})});d.displayName="Row";const i=d},1719:(e,s,r)=>{r.d(s,{A:()=>_});var a=r(8139),t=r.n(a),n=r(5043),o=r(1969),l=r(6618),d=r(7852),i=r(4488),c=r(579);const u=(0,i.A)("h4");u.displayName="DivStyledAsH4";const h=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n=u,...o}=e;return a=(0,d.oU)(a,"alert-heading"),(0,c.jsx)(n,{ref:s,className:t()(r,a),...o})});h.displayName="AlertHeading";const p=h;var m=r(7071);const w=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n=m.A,...o}=e;return a=(0,d.oU)(a,"alert-link"),(0,c.jsx)(n,{ref:s,className:t()(r,a),...o})});w.displayName="AlertLink";const f=w;var x=r(8072),A=r(5632);const j=n.forwardRef((e,s)=>{const{bsPrefix:r,show:a=!0,closeLabel:n="Close alert",closeVariant:i,className:u,children:h,variant:p="primary",onClose:m,dismissible:w,transition:f=x.A,...j}=(0,o.Zw)(e,{show:"onClose"}),_=(0,d.oU)(r,"alert"),b=(0,l.A)(e=>{m&&m(!1,e)}),v=!0===f?x.A:f,g=(0,c.jsxs)("div",{role:"alert",...v?void 0:j,ref:s,className:t()(u,_,p&&`${_}-${p}`,w&&`${_}-dismissible`),children:[w&&(0,c.jsx)(A.A,{onClick:b,"aria-label":n,variant:i}),h]});return v?(0,c.jsx)(v,{unmountOnExit:!0,...j,ref:void 0,in:a,children:g}):a?g:null});j.displayName="Alert";const _=Object.assign(j,{Link:f,Heading:p})}}]);
//# sourceMappingURL=652.bdbef98c.chunk.js.map