# KYC 身份审核功能实现总结

## 概述
已成功修改 KycPage.js 页面，使顾客可以真正提交身份审核。身份证正反面照片现在存储在 WordPress 的媒体库中（/wp-content/uploads/...），插件获取上传结果的 URL 并将其写入 Supabase 的 customer_profiles.id_img_front / id_img_back 字段。

## 主要修改

### 1. 后端 API 修改 (fil-platform-plugin/includes/api-routes.php)

#### 新增 API 端点：
- **`/upload-kyc-image`** - 单独的文件上传端点
- **`/submit-kyc`** - 完整的 KYC 提交端点（包含文件上传和数据更新）

#### 新增功能：
- `upload_kyc_image()` - 处理文件上传到 WordPress 媒体库
- `submit_kyc()` - 处理完整的 KYC 提交流程
- `get_upload_error_message()` - 处理文件上传错误信息

#### 文件上传特性：
- 支持 JPEG, PNG, GIF 格式
- 最大文件大小：5MB
- 自动生成唯一文件名：`kyc_{用户邮箱}_{时间戳}_{原文件名}`
- 文件存储在 WordPress 媒体库 (/wp-content/uploads/)
- 自动生成缩略图和元数据

### 2. 前端修改 (fil-platform-plugin/frontend/src/pages/customer/KycPage.js)

#### 主要变更：
- 移除了 Supabase 存储的文件上传逻辑
- 改用 WordPress REST API 进行文件上传和数据提交
- 使用 FormData 发送多部分表单数据
- 简化了图片显示逻辑（直接使用 WordPress 媒体 URL）

#### 提交流程：
1. 用户填写真实姓名和身份证号码
2. 上传身份证正反面照片
3. 点击提交按钮
4. 前端将数据和文件打包为 FormData
5. 发送到 `/submit-kyc` API 端点
6. 后端处理文件上传到 WordPress 媒体库
7. 后端将数据（包括图片 URL）更新到 Supabase
8. 返回成功结果给前端

## 技术实现细节

### 文件上传流程：
1. **前端**：使用 FormData 包装文件和表单数据
2. **WordPress API**：接收文件并使用 `wp_handle_upload()` 处理
3. **媒体库**：文件存储在 `/wp-content/uploads/` 目录
4. **数据库**：WordPress 自动创建 attachment 记录
5. **Supabase**：存储文件的完整 URL 路径

### 安全措施：
- 用户权限验证（必须登录）
- 文件类型验证（仅允许图片格式）
- 文件大小限制（最大 5MB）
- 唯一文件名生成（防止冲突）
- WordPress nonce 验证

### 数据流：
```
用户上传 → WordPress 媒体库 → 获取 URL → 存储到 Supabase → 前端显示
```

## 使用方法

### 用户操作：
1. 登录系统
2. 进入 KYC 页面
3. 填写真实姓名和身份证号码
4. 上传身份证正反面照片
5. 点击提交审核
6. 等待管理员审核

### 管理员操作：
- 可以在 WordPress 媒体库中查看上传的身份证照片
- 可以在 Supabase 中查看和管理 KYC 数据
- 可以更新 verify_status 字段进行审核

## 文件结构

### 上传的文件命名规则：
```
kyc_{用户邮箱}_{时间戳}_{原文件名}
例如：kyc_user_at_example.com_1704067200_front.jpg
```

### Supabase 数据结构：
```sql
customer_profiles:
- user_id (UUID)
- real_name (varchar)
- id_number (varchar)
- id_img_front (varchar) -- WordPress 媒体 URL
- id_img_back (varchar)  -- WordPress 媒体 URL
- verify_status (varchar) -- 'pending', 'approved', 'rejected'
```

## 注意事项

1. **权限设置**：确保 WordPress 上传目录有正确的写入权限
2. **Supabase 配置**：需要在 WordPress 设置中配置正确的 Supabase URL 和 Service Key
3. **文件清理**：建议定期清理未使用的媒体文件
4. **备份**：重要的身份证照片应该包含在网站备份中

## 测试建议

1. 测试不同格式的图片上传
2. 测试大文件上传（接近 5MB 限制）
3. 测试无效文件格式的处理
4. 测试网络中断时的错误处理
5. 验证 Supabase 数据更新是否正确
6. 测试图片在前端的正确显示

## 构建状态

前端已成功构建，所有修改已生效。可以直接在 WordPress 环境中测试 KYC 功能。
