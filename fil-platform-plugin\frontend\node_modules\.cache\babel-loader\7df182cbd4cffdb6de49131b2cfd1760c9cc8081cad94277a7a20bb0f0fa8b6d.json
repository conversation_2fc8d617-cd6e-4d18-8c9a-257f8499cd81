{"ast": null, "code": "import React,{useState}from'react';import{Container,Row,Col,Card,Form,Button,Alert}from'react-bootstrap';import{useTranslation}from'react-i18next';import{getSupabase}from'../../supabaseClient';import{useNavigate}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ChangeLoginPass=()=>{const{t}=useTranslation();const[currentPassword,setCurrentPassword]=useState('');const[newPassword,setNewPassword]=useState('');const[confirmPassword,setConfirmPassword]=useState('');const[loading,setLoading]=useState(false);const[message,setMessage]=useState('');const[error,setError]=useState('');const navigate=useNavigate();const handleSubmit=async e=>{e.preventDefault();setError('');setMessage('');// 验证新密码和确认密码是否一致\nif(newPassword!==confirmPassword){setError(t('passwords_do_not_match'));return;}// 验证新密码长度\nif(newPassword.length<6){setError(t('password_too_short'));return;}setLoading(true);try{const supabase=getSupabase();if(!supabase){throw new Error(t('backend_connection_failed'));}// 获取当前用户\nconst{data:{user},error:userError}=await supabase.auth.getUser();if(userError||!user){throw new Error(t('user_not_authenticated'));}// 首先验证当前密码是否正确\n// 通过重新登录来验证当前密码\nconst{error:signInError}=await supabase.auth.signInWithPassword({email:user.email,password:currentPassword});if(signInError){throw new Error(t('current_password_incorrect'));}// 更新密码\nconst{error:updateError}=await supabase.auth.updateUser({password:newPassword});if(updateError){throw updateError;}setMessage(t('password_updated_successfully'));// 清空表单\nsetCurrentPassword('');setNewPassword('');setConfirmPassword('');// 3秒后跳转回我的账户页面\nsetTimeout(()=>{navigate('/my');},3000);}catch(err){console.error('Password update error:',err);setError(err.message||t('password_update_failed'));}setLoading(false);};return/*#__PURE__*/_jsx(Container,{children:/*#__PURE__*/_jsx(Row,{className:\"justify-content-center\",children:/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Card.Header,{children:/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0\",children:t('change_login_password')})}),/*#__PURE__*/_jsxs(Card.Body,{children:[error&&/*#__PURE__*/_jsx(Alert,{variant:\"danger\",children:error}),message&&/*#__PURE__*/_jsx(Alert,{variant:\"success\",children:message}),/*#__PURE__*/_jsxs(Form,{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:t('current_password')}),/*#__PURE__*/_jsx(Form.Control,{type:\"password\",value:currentPassword,onChange:e=>setCurrentPassword(e.target.value),required:true,placeholder:t('enter_current_password')})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:t('new_password')}),/*#__PURE__*/_jsx(Form.Control,{type:\"password\",value:newPassword,onChange:e=>setNewPassword(e.target.value),required:true,minLength:6,placeholder:t('enter_new_password')}),/*#__PURE__*/_jsx(Form.Text,{className:\"text-muted\",children:t('password_requirements')})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:t('confirm_new_password')}),/*#__PURE__*/_jsx(Form.Control,{type:\"password\",value:confirmPassword,onChange:e=>setConfirmPassword(e.target.value),required:true,minLength:6,placeholder:t('confirm_new_password')})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-grid gap-2\",children:[/*#__PURE__*/_jsx(Button,{variant:\"primary\",type:\"submit\",disabled:loading,children:loading?t('updating'):t('update_password')}),/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>navigate('/my'),disabled:loading,children:t('cancel')})]})]})]})]})})})});};export default ChangeLoginPass;", "map": {"version": 3, "names": ["React", "useState", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "useTranslation", "getSupabase", "useNavigate", "jsx", "_jsx", "jsxs", "_jsxs", "ChangeLoginPass", "t", "currentPassword", "setCurrentPassword", "newPassword", "setNewPassword", "confirmPassword", "setConfirmPassword", "loading", "setLoading", "message", "setMessage", "error", "setError", "navigate", "handleSubmit", "e", "preventDefault", "length", "supabase", "Error", "data", "user", "userError", "auth", "getUser", "signInError", "signInWithPassword", "email", "password", "updateError", "updateUser", "setTimeout", "err", "console", "children", "className", "md", "Header", "Body", "variant", "onSubmit", "Group", "Label", "Control", "type", "value", "onChange", "target", "required", "placeholder", "<PERSON><PERSON><PERSON><PERSON>", "Text", "disabled", "onClick"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/customer/ChangeLoginPass.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Container, Row, Col, Card, Form, Button, Alert } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { getSupabase } from '../../supabaseClient';\nimport { useNavigate } from 'react-router-dom';\n\nconst ChangeLoginPass = () => {\n    const { t } = useTranslation();\n    const [currentPassword, setCurrentPassword] = useState('');\n    const [newPassword, setNewPassword] = useState('');\n    const [confirmPassword, setConfirmPassword] = useState('');\n    const [loading, setLoading] = useState(false);\n    const [message, setMessage] = useState('');\n    const [error, setError] = useState('');\n    const navigate = useNavigate();\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setError('');\n        setMessage('');\n\n        // 验证新密码和确认密码是否一致\n        if (newPassword !== confirmPassword) {\n            setError(t('passwords_do_not_match'));\n            return;\n        }\n\n        // 验证新密码长度\n        if (newPassword.length < 6) {\n            setError(t('password_too_short'));\n            return;\n        }\n\n        setLoading(true);\n\n        try {\n            const supabase = getSupabase();\n            if (!supabase) {\n                throw new Error(t('backend_connection_failed'));\n            }\n\n            // 获取当前用户\n            const { data: { user }, error: userError } = await supabase.auth.getUser();\n            if (userError || !user) {\n                throw new Error(t('user_not_authenticated'));\n            }\n\n            // 首先验证当前密码是否正确\n            // 通过重新登录来验证当前密码\n            const { error: signInError } = await supabase.auth.signInWithPassword({\n                email: user.email,\n                password: currentPassword,\n            });\n\n            if (signInError) {\n                throw new Error(t('current_password_incorrect'));\n            }\n\n            // 更新密码\n            const { error: updateError } = await supabase.auth.updateUser({\n                password: newPassword\n            });\n\n            if (updateError) {\n                throw updateError;\n            }\n\n            setMessage(t('password_updated_successfully'));\n            \n            // 清空表单\n            setCurrentPassword('');\n            setNewPassword('');\n            setConfirmPassword('');\n\n            // 3秒后跳转回我的账户页面\n            setTimeout(() => {\n                navigate('/my');\n            }, 3000);\n\n        } catch (err) {\n            console.error('Password update error:', err);\n            setError(err.message || t('password_update_failed'));\n        }\n\n        setLoading(false);\n    };\n\n    return (\n        <Container>\n            <Row className=\"justify-content-center\">\n                <Col md={6}>\n                    <Card>\n                        <Card.Header>\n                            <h4 className=\"mb-0\">{t('change_login_password')}</h4>\n                        </Card.Header>\n                        <Card.Body>\n                            {error && <Alert variant=\"danger\">{error}</Alert>}\n                            {message && <Alert variant=\"success\">{message}</Alert>}\n                            \n                            <Form onSubmit={handleSubmit}>\n                                <Form.Group className=\"mb-3\">\n                                    <Form.Label>{t('current_password')}</Form.Label>\n                                    <Form.Control\n                                        type=\"password\"\n                                        value={currentPassword}\n                                        onChange={(e) => setCurrentPassword(e.target.value)}\n                                        required\n                                        placeholder={t('enter_current_password')}\n                                    />\n                                </Form.Group>\n\n                                <Form.Group className=\"mb-3\">\n                                    <Form.Label>{t('new_password')}</Form.Label>\n                                    <Form.Control\n                                        type=\"password\"\n                                        value={newPassword}\n                                        onChange={(e) => setNewPassword(e.target.value)}\n                                        required\n                                        minLength={6}\n                                        placeholder={t('enter_new_password')}\n                                    />\n                                    <Form.Text className=\"text-muted\">\n                                        {t('password_requirements')}\n                                    </Form.Text>\n                                </Form.Group>\n\n                                <Form.Group className=\"mb-3\">\n                                    <Form.Label>{t('confirm_new_password')}</Form.Label>\n                                    <Form.Control\n                                        type=\"password\"\n                                        value={confirmPassword}\n                                        onChange={(e) => setConfirmPassword(e.target.value)}\n                                        required\n                                        minLength={6}\n                                        placeholder={t('confirm_new_password')}\n                                    />\n                                </Form.Group>\n\n                                <div className=\"d-grid gap-2\">\n                                    <Button \n                                        variant=\"primary\" \n                                        type=\"submit\" \n                                        disabled={loading}\n                                    >\n                                        {loading ? t('updating') : t('update_password')}\n                                    </Button>\n                                    <Button \n                                        variant=\"secondary\" \n                                        onClick={() => navigate('/my')}\n                                        disabled={loading}\n                                    >\n                                        {t('cancel')}\n                                    </Button>\n                                </div>\n                            </Form>\n                        </Card.Body>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default ChangeLoginPass;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,IAAI,CAAEC,MAAM,CAAEC,KAAK,KAAQ,iBAAiB,CAChF,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAEC,CAAE,CAAC,CAAGR,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACS,eAAe,CAAEC,kBAAkB,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACmB,WAAW,CAAEC,cAAc,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACqB,eAAe,CAAEC,kBAAkB,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACuB,OAAO,CAAEC,UAAU,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACyB,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC2B,KAAK,CAAEC,QAAQ,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAA6B,QAAQ,CAAGnB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAoB,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBJ,QAAQ,CAAC,EAAE,CAAC,CACZF,UAAU,CAAC,EAAE,CAAC,CAEd;AACA,GAAIP,WAAW,GAAKE,eAAe,CAAE,CACjCO,QAAQ,CAACZ,CAAC,CAAC,wBAAwB,CAAC,CAAC,CACrC,OACJ,CAEA;AACA,GAAIG,WAAW,CAACc,MAAM,CAAG,CAAC,CAAE,CACxBL,QAAQ,CAACZ,CAAC,CAAC,oBAAoB,CAAC,CAAC,CACjC,OACJ,CAEAQ,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACA,KAAM,CAAAU,QAAQ,CAAGzB,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACyB,QAAQ,CAAE,CACX,KAAM,IAAI,CAAAC,KAAK,CAACnB,CAAC,CAAC,2BAA2B,CAAC,CAAC,CACnD,CAEA;AACA,KAAM,CAAEoB,IAAI,CAAE,CAAEC,IAAK,CAAC,CAAEV,KAAK,CAAEW,SAAU,CAAC,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAACC,OAAO,CAAC,CAAC,CAC1E,GAAIF,SAAS,EAAI,CAACD,IAAI,CAAE,CACpB,KAAM,IAAI,CAAAF,KAAK,CAACnB,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAChD,CAEA;AACA;AACA,KAAM,CAAEW,KAAK,CAAEc,WAAY,CAAC,CAAG,KAAM,CAAAP,QAAQ,CAACK,IAAI,CAACG,kBAAkB,CAAC,CAClEC,KAAK,CAAEN,IAAI,CAACM,KAAK,CACjBC,QAAQ,CAAE3B,eACd,CAAC,CAAC,CAEF,GAAIwB,WAAW,CAAE,CACb,KAAM,IAAI,CAAAN,KAAK,CAACnB,CAAC,CAAC,4BAA4B,CAAC,CAAC,CACpD,CAEA;AACA,KAAM,CAAEW,KAAK,CAAEkB,WAAY,CAAC,CAAG,KAAM,CAAAX,QAAQ,CAACK,IAAI,CAACO,UAAU,CAAC,CAC1DF,QAAQ,CAAEzB,WACd,CAAC,CAAC,CAEF,GAAI0B,WAAW,CAAE,CACb,KAAM,CAAAA,WAAW,CACrB,CAEAnB,UAAU,CAACV,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAE9C;AACAE,kBAAkB,CAAC,EAAE,CAAC,CACtBE,cAAc,CAAC,EAAE,CAAC,CAClBE,kBAAkB,CAAC,EAAE,CAAC,CAEtB;AACAyB,UAAU,CAAC,IAAM,CACblB,QAAQ,CAAC,KAAK,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CAEZ,CAAE,MAAOmB,GAAG,CAAE,CACVC,OAAO,CAACtB,KAAK,CAAC,wBAAwB,CAAEqB,GAAG,CAAC,CAC5CpB,QAAQ,CAACoB,GAAG,CAACvB,OAAO,EAAIT,CAAC,CAAC,wBAAwB,CAAC,CAAC,CACxD,CAEAQ,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAED,mBACIZ,IAAA,CAACX,SAAS,EAAAiD,QAAA,cACNtC,IAAA,CAACV,GAAG,EAACiD,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cACnCtC,IAAA,CAACT,GAAG,EAACiD,EAAE,CAAE,CAAE,CAAAF,QAAA,cACPpC,KAAA,CAACV,IAAI,EAAA8C,QAAA,eACDtC,IAAA,CAACR,IAAI,CAACiD,MAAM,EAAAH,QAAA,cACRtC,IAAA,OAAIuC,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAElC,CAAC,CAAC,uBAAuB,CAAC,CAAK,CAAC,CAC7C,CAAC,cACdF,KAAA,CAACV,IAAI,CAACkD,IAAI,EAAAJ,QAAA,EACLvB,KAAK,eAAIf,IAAA,CAACL,KAAK,EAACgD,OAAO,CAAC,QAAQ,CAAAL,QAAA,CAAEvB,KAAK,CAAQ,CAAC,CAChDF,OAAO,eAAIb,IAAA,CAACL,KAAK,EAACgD,OAAO,CAAC,SAAS,CAAAL,QAAA,CAAEzB,OAAO,CAAQ,CAAC,cAEtDX,KAAA,CAACT,IAAI,EAACmD,QAAQ,CAAE1B,YAAa,CAAAoB,QAAA,eACzBpC,KAAA,CAACT,IAAI,CAACoD,KAAK,EAACN,SAAS,CAAC,MAAM,CAAAD,QAAA,eACxBtC,IAAA,CAACP,IAAI,CAACqD,KAAK,EAAAR,QAAA,CAAElC,CAAC,CAAC,kBAAkB,CAAC,CAAa,CAAC,cAChDJ,IAAA,CAACP,IAAI,CAACsD,OAAO,EACTC,IAAI,CAAC,UAAU,CACfC,KAAK,CAAE5C,eAAgB,CACvB6C,QAAQ,CAAG/B,CAAC,EAAKb,kBAAkB,CAACa,CAAC,CAACgC,MAAM,CAACF,KAAK,CAAE,CACpDG,QAAQ,MACRC,WAAW,CAAEjD,CAAC,CAAC,wBAAwB,CAAE,CAC5C,CAAC,EACM,CAAC,cAEbF,KAAA,CAACT,IAAI,CAACoD,KAAK,EAACN,SAAS,CAAC,MAAM,CAAAD,QAAA,eACxBtC,IAAA,CAACP,IAAI,CAACqD,KAAK,EAAAR,QAAA,CAAElC,CAAC,CAAC,cAAc,CAAC,CAAa,CAAC,cAC5CJ,IAAA,CAACP,IAAI,CAACsD,OAAO,EACTC,IAAI,CAAC,UAAU,CACfC,KAAK,CAAE1C,WAAY,CACnB2C,QAAQ,CAAG/B,CAAC,EAAKX,cAAc,CAACW,CAAC,CAACgC,MAAM,CAACF,KAAK,CAAE,CAChDG,QAAQ,MACRE,SAAS,CAAE,CAAE,CACbD,WAAW,CAAEjD,CAAC,CAAC,oBAAoB,CAAE,CACxC,CAAC,cACFJ,IAAA,CAACP,IAAI,CAAC8D,IAAI,EAAChB,SAAS,CAAC,YAAY,CAAAD,QAAA,CAC5BlC,CAAC,CAAC,uBAAuB,CAAC,CACpB,CAAC,EACJ,CAAC,cAEbF,KAAA,CAACT,IAAI,CAACoD,KAAK,EAACN,SAAS,CAAC,MAAM,CAAAD,QAAA,eACxBtC,IAAA,CAACP,IAAI,CAACqD,KAAK,EAAAR,QAAA,CAAElC,CAAC,CAAC,sBAAsB,CAAC,CAAa,CAAC,cACpDJ,IAAA,CAACP,IAAI,CAACsD,OAAO,EACTC,IAAI,CAAC,UAAU,CACfC,KAAK,CAAExC,eAAgB,CACvByC,QAAQ,CAAG/B,CAAC,EAAKT,kBAAkB,CAACS,CAAC,CAACgC,MAAM,CAACF,KAAK,CAAE,CACpDG,QAAQ,MACRE,SAAS,CAAE,CAAE,CACbD,WAAW,CAAEjD,CAAC,CAAC,sBAAsB,CAAE,CAC1C,CAAC,EACM,CAAC,cAEbF,KAAA,QAAKqC,SAAS,CAAC,cAAc,CAAAD,QAAA,eACzBtC,IAAA,CAACN,MAAM,EACHiD,OAAO,CAAC,SAAS,CACjBK,IAAI,CAAC,QAAQ,CACbQ,QAAQ,CAAE7C,OAAQ,CAAA2B,QAAA,CAEjB3B,OAAO,CAAGP,CAAC,CAAC,UAAU,CAAC,CAAGA,CAAC,CAAC,iBAAiB,CAAC,CAC3C,CAAC,cACTJ,IAAA,CAACN,MAAM,EACHiD,OAAO,CAAC,WAAW,CACnBc,OAAO,CAAEA,CAAA,GAAMxC,QAAQ,CAAC,KAAK,CAAE,CAC/BuC,QAAQ,CAAE7C,OAAQ,CAAA2B,QAAA,CAEjBlC,CAAC,CAAC,QAAQ,CAAC,CACR,CAAC,EACR,CAAC,EACJ,CAAC,EACA,CAAC,EACV,CAAC,CACN,CAAC,CACL,CAAC,CACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}