"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[738],{1719:(e,s,a)=>{a.d(s,{A:()=>y});var t=a(8139),i=a.n(t),r=a(5043),n=a(1969),l=a(6618),d=a(7852),o=a(4488),c=a(579);const m=(0,o.A)("h4");m.displayName="DivStyledAsH4";const p=r.forwardRef((e,s)=>{let{className:a,bsPrefix:t,as:r=m,...n}=e;return t=(0,d.oU)(t,"alert-heading"),(0,c.jsx)(r,{ref:s,className:i()(a,t),...n})});p.displayName="AlertHeading";const u=p;var f=a(7071);const g=r.forwardRef((e,s)=>{let{className:a,bsPrefix:t,as:r=f.A,...n}=e;return t=(0,d.oU)(t,"alert-link"),(0,c.jsx)(r,{ref:s,className:i()(a,t),...n})});g.displayName="AlertLink";const _=g;var b=a(8072),x=a(5632);const h=r.forwardRef((e,s)=>{const{bsPrefix:a,show:t=!0,closeLabel:r="Close alert",closeVariant:o,className:m,children:p,variant:u="primary",onClose:f,dismissible:g,transition:_=b.A,...h}=(0,n.Zw)(e,{show:"onClose"}),y=(0,d.oU)(a,"alert"),j=(0,l.A)(e=>{f&&f(!1,e)}),v=!0===_?b.A:_,A=(0,c.jsxs)("div",{role:"alert",...v?void 0:h,ref:s,className:i()(m,y,u&&`${y}-${u}`,g&&`${y}-dismissible`),children:[g&&(0,c.jsx)(x.A,{onClick:j,"aria-label":r,variant:o}),p]});return v?(0,c.jsx)(v,{unmountOnExit:!0,...h,ref:void 0,in:t,children:A}):t?A:null});h.displayName="Alert";const y=Object.assign(h,{Link:_,Heading:u})},2738:(e,s,a)=>{a.r(s),a.d(s,{default:()=>p});var t=a(5043),i=a(3519),r=a(8628),n=a(1719),l=a(3722),d=a(4282),o=a(4312),c=a(4117),m=a(579);const p=()=>{const{t:e}=(0,c.Bd)(),[s,a]=(0,t.useState)(""),[p,u]=(0,t.useState)(""),[f,g]=(0,t.useState)(null),[_,b]=(0,t.useState)(null),[x,h]=(0,t.useState)("pending"),[y,j]=(0,t.useState)(!0),[v,A]=(0,t.useState)(!1),[w,k]=(0,t.useState)({type:"",text:""});(0,t.useEffect)(()=>{(async()=>{const s=(0,o.b)();if(!s)return;const{data:{user:t}}=await s.auth.getUser();if(!t)return void j(!1);const{data:i,error:r}=await s.from("customer_profiles").select("real_name, id_number, id_img_front, id_img_back, verify_status").eq("user_id",t.id).single();r&&"PGRST116"!==r.code?(console.error("Error fetching KYC status:",r),k({type:"danger",text:e("failed_to_load_kyc_status")})):i&&(a(i.real_name||""),u(i.id_number||""),g(i.id_img_front||null),b(i.id_img_back||null),h(i.verify_status||"pending")),j(!1)})()},[]);const N=(e,s)=>{e.target.files&&e.target.files[0]&&s(e.target.files[0])};return y?(0,m.jsx)("div",{children:e("loading_kyc_status")}):(0,m.jsxs)(i.A,{children:[(0,m.jsx)("h2",{className:"mb-4",children:e("kyc_verification")}),(0,m.jsx)(r.A,{children:(0,m.jsxs)(r.A.Body,{children:[w.text&&(0,m.jsx)(n.A,{variant:w.type,children:w.text}),"approved"===x&&(0,m.jsx)(n.A,{variant:"success",children:e("kyc_approved")}),"pending"===x&&(0,m.jsx)(n.A,{variant:"info",children:e("kyc_pending_review")}),"rejected"===x&&(0,m.jsx)(n.A,{variant:"danger",children:e("kyc_rejected")}),(0,m.jsxs)(l.A,{onSubmit:async a=>{a.preventDefault(),A(!0),k({type:"",text:""});try{const a=new FormData;a.append("real_name",s),a.append("id_number",p),f instanceof File&&a.append("id_img_front",f),_ instanceof File&&a.append("id_img_back",_);const t=await fetch(`${window.wpData.apiUrl}submit-kyc`,{method:"POST",body:a,headers:{"X-WP-Nonce":window.wpData.nonce}}),i=await t.json();if(!i.success)throw new Error(i.message||"Failed to submit KYC");i.files&&i.files.id_img_front&&g(i.files.id_img_front.url),i.files&&i.files.id_img_back&&b(i.files.id_img_back.url),k({type:"success",text:e("kyc_submit_success")}),h("pending"),i.errors&&i.errors.length>0&&console.warn("Upload warnings:",i.errors)}catch(t){console.error("KYC submission error:",t),k({type:"danger",text:e("failed_to_submit_kyc")+": "+t.message})}A(!1)},children:[(0,m.jsxs)(l.A.Group,{className:"mb-3",children:[(0,m.jsx)(l.A.Label,{children:e("real_name")}),(0,m.jsx)(l.A.Control,{type:"text",value:s,onChange:e=>a(e.target.value),required:!0,disabled:"pending"===x||"approved"===x})]}),(0,m.jsxs)(l.A.Group,{className:"mb-3",children:[(0,m.jsx)(l.A.Label,{children:e("id_number")}),(0,m.jsx)(l.A.Control,{type:"text",value:p,onChange:e=>u(e.target.value),required:!0,disabled:"pending"===x||"approved"===x})]}),(0,m.jsxs)(l.A.Group,{className:"mb-3",children:[(0,m.jsx)(l.A.Label,{children:e("id_front")}),(0,m.jsx)(l.A.Control,{type:"file",onChange:e=>N(e,g),accept:"image/*",disabled:"pending"===x||"approved"===x}),f&&"string"===typeof f&&(0,m.jsx)("img",{src:f,alt:"ID Front",className:"img-thumbnail mt-2",style:{maxWidth:"200px"}})]}),(0,m.jsxs)(l.A.Group,{className:"mb-3",children:[(0,m.jsx)(l.A.Label,{children:e("id_back")}),(0,m.jsx)(l.A.Control,{type:"file",onChange:e=>N(e,b),accept:"image/*",disabled:"pending"===x||"approved"===x}),_&&"string"===typeof _&&(0,m.jsx)("img",{src:_,alt:"ID Back",className:"img-thumbnail mt-2",style:{maxWidth:"200px"}})]}),(0,m.jsx)(d.A,{variant:"primary",type:"submit",disabled:v||"pending"===x||"approved"===x,children:e(v?"submitting":"submit_review")})]})]})})]})}}}]);
//# sourceMappingURL=738.92305dd2.chunk.js.map