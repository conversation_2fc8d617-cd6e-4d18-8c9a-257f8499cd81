{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,ListGroup,Table,Button,Nav}from'react-bootstrap';import{useTranslation}from'react-i18next';import{getSupabase}from'../../supabaseClient';import{Link}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MyAccountPage=()=>{const{t}=useTranslation();const[assets,setAssets]=useState([]);const[loading,setLoading]=useState(true);const[activeTab,setActiveTab]=useState('overview');// overview, deposit, withdraw, exchange\nuseEffect(()=>{const fetchAssets=async()=>{const supabase=getSupabase();if(!supabase)return;setLoading(true);const{data:{user}}=await supabase.auth.getUser();if(!user){setLoading(false);return;// User not logged in\n}const{data,error}=await supabase.from('user_assets').select('*').eq('user_id',user.id);if(error){console.error('Error fetching assets:',error);}else{setAssets(data);}setLoading(false);};fetchAssets();},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{children:t('loading_wallet')});}const renderContent=()=>{switch(activeTab){case'overview':return/*#__PURE__*/_jsxs(Table,{striped:true,bordered:true,hover:true,responsive:true,children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:t('currency')}),/*#__PURE__*/_jsx(\"th\",{children:t('available_balance')}),/*#__PURE__*/_jsx(\"th\",{children:t('locked_balance')}),/*#__PURE__*/_jsx(\"th\",{children:t('total_balance')}),/*#__PURE__*/_jsx(\"th\",{children:t('withdrawn')})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:assets.length===0?/*#__PURE__*/_jsx(\"tr\",{children:/*#__PURE__*/_jsx(\"td\",{colSpan:\"5\",className:\"text-center\",children:t('no_assets')})}):assets.map(asset=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:asset.currency_code}),/*#__PURE__*/_jsx(\"td\",{children:asset.available_balance}),/*#__PURE__*/_jsx(\"td\",{children:asset.balance_locked}),/*#__PURE__*/_jsx(\"td\",{children:asset.balance_total}),/*#__PURE__*/_jsx(\"td\",{children:asset.withdrawn_total})]},asset.currency_code))})]});case'deposit':return/*#__PURE__*/_jsx(\"div\",{children:t('deposit_coming_soon')});// Placeholder for deposit UI\ncase'withdraw':return/*#__PURE__*/_jsx(\"div\",{children:t('withdraw_coming_soon')});// Placeholder for withdraw UI\ncase'exchange':return/*#__PURE__*/_jsx(\"div\",{children:t('exchange_coming_soon')});// Placeholder for exchange UI\ndefault:return null;}};return/*#__PURE__*/_jsxs(Container,{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"mb-4\",children:t('my_wallet')}),/*#__PURE__*/_jsx(Row,{children:/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Card.Header,{children:/*#__PURE__*/_jsxs(Nav,{variant:\"tabs\",defaultActiveKey:\"overview\",onSelect:selectedKey=>setActiveTab(selectedKey),children:[/*#__PURE__*/_jsx(Nav.Item,{children:/*#__PURE__*/_jsx(Nav.Link,{eventKey:\"overview\",children:t('overview')})}),/*#__PURE__*/_jsx(Nav.Item,{children:/*#__PURE__*/_jsx(Nav.Link,{eventKey:\"deposit\",children:t('deposit')})}),/*#__PURE__*/_jsx(Nav.Item,{children:/*#__PURE__*/_jsx(Nav.Link,{eventKey:\"withdraw\",children:t('withdraw')})}),/*#__PURE__*/_jsx(Nav.Item,{children:/*#__PURE__*/_jsx(Nav.Link,{eventKey:\"exchange\",children:t('exchange')})})]})}),/*#__PURE__*/_jsx(Card.Body,{children:renderContent()})]})})}),/*#__PURE__*/_jsx(\"h2\",{className:\"mt-5 mb-4\",children:t('my_account')}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Card.Header,{children:t('personal_info')}),/*#__PURE__*/_jsxs(ListGroup,{variant:\"flush\",children:[/*#__PURE__*/_jsx(ListGroup.Item,{children:/*#__PURE__*/_jsx(Link,{to:\"/my/kyc\",children:t('kyc_verification')})}),/*#__PURE__*/_jsx(ListGroup.Item,{children:/*#__PURE__*/_jsx(Link,{to:\"/my/change-login-pass\",children:t('change_login_password')})}),/*#__PURE__*/_jsx(ListGroup.Item,{children:/*#__PURE__*/_jsx(Link,{to:\"/my/change-withdraw-pass\",children:t('change_withdraw_password')})})]})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(Card.Header,{children:t('my_recommendations')}),/*#__PURE__*/_jsx(ListGroup,{variant:\"flush\",children:/*#__PURE__*/_jsx(ListGroup.Item,{children:/*#__PURE__*/_jsx(Link,{to:\"/my/recommend\",children:t('view_my_recommendations')})})})]})})]})]});};export default MyAccountPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "ListGroup", "Table", "<PERSON><PERSON>", "Nav", "useTranslation", "getSupabase", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "MyAccountPage", "t", "assets", "setAssets", "loading", "setLoading", "activeTab", "setActiveTab", "fetchAssets", "supabase", "data", "user", "auth", "getUser", "error", "from", "select", "eq", "id", "console", "children", "renderContent", "striped", "bordered", "hover", "responsive", "length", "colSpan", "className", "map", "asset", "currency_code", "available_balance", "balance_locked", "balance_total", "withdrawn_total", "Header", "variant", "defaultActiveKey", "onSelect", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "eventKey", "Body", "md", "to"], "sources": ["D:/New_System/fil-platform-plugin/frontend/src/pages/customer/MyAccountPage.js"], "sourcesContent": ["\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, ListGroup, Table, Button, Nav } from 'react-bootstrap';\nimport { useTranslation } from 'react-i18next';\nimport { getSupabase } from '../../supabaseClient';\nimport { Link } from 'react-router-dom';\n\nconst MyAccountPage = () => {\n    const { t } = useTranslation();\n    const [assets, setAssets] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [activeTab, setActiveTab] = useState('overview'); // overview, deposit, withdraw, exchange\n\n    useEffect(() => {\n        const fetchAssets = async () => {\n            const supabase = getSupabase();\n            if (!supabase) return;\n    \n            setLoading(true);\n            const { data: { user } } = await supabase.auth.getUser();\n    \n            if (!user) {\n                setLoading(false);\n                return; // User not logged in\n            }\n    \n            const { data, error } = await supabase\n                .from('user_assets')\n                .select('*')\n                .eq('user_id', user.id);\n    \n            if (error) {\n                console.error('Error fetching assets:', error);\n            } else {\n                setAssets(data);\n            }\n            setLoading(false);\n        };\n    \n        fetchAssets();\n    }, []);\n    \n    if (loading) {\n        return <div>{t('loading_wallet')}</div>;\n    }\n    \n    const renderContent = () => {\n        switch (activeTab) {\n            case 'overview':\n                return (\n                    <Table striped bordered hover responsive>\n                        <thead>\n                            <tr>\n                                <th>{t('currency')}</th>\n                                <th>{t('available_balance')}</th>\n                                <th>{t('locked_balance')}</th>\n                                <th>{t('total_balance')}</th>\n                                <th>{t('withdrawn')}</th>\n                            </tr>\n                        </thead>\n                        <tbody>\n                            {assets.length === 0 ? (\n                                <tr>\n                                    <td colSpan=\"5\" className=\"text-center\">{t('no_assets')}</td>\n                                </tr>\n                            ) : (\n                                assets.map(asset => (\n                                    <tr key={asset.currency_code}>\n                                        <td>{asset.currency_code}</td>\n                                        <td>{asset.available_balance}</td>\n                                        <td>{asset.balance_locked}</td>\n                                        <td>{asset.balance_total}</td>\n                                        <td>{asset.withdrawn_total}</td>\n                                    </tr>\n                                ))\n                            )}\n                        </tbody>\n                    </Table>\n                );\n            case 'deposit':\n                return <div>{t('deposit_coming_soon')}</div>; // Placeholder for deposit UI\n            case 'withdraw':\n                return <div>{t('withdraw_coming_soon')}</div>; // Placeholder for withdraw UI\n            case 'exchange':\n                return <div>{t('exchange_coming_soon')}</div>; // Placeholder for exchange UI\n            default:\n                return null;\n        }\n    };\n\n    return (\n        <Container>\n            <h2 className=\"mb-4\">{t('my_wallet')}</h2>\n                <Row>\n                    <Col>\n                        <Card>\n                            <Card.Header>\n                                <Nav variant=\"tabs\" defaultActiveKey=\"overview\" onSelect={(selectedKey) => setActiveTab(selectedKey)}>\n                                    <Nav.Item>\n                                        <Nav.Link eventKey=\"overview\">{t('overview')}</Nav.Link>\n                                    </Nav.Item>\n                                    <Nav.Item>\n                                        <Nav.Link eventKey=\"deposit\">{t('deposit')}</Nav.Link>\n                                    </Nav.Item>\n                                    <Nav.Item>\n                                        <Nav.Link eventKey=\"withdraw\">{t('withdraw')}</Nav.Link>\n                                    </Nav.Item>\n                                    <Nav.Item>\n                                        <Nav.Link eventKey=\"exchange\">{t('exchange')}</Nav.Link>\n                                    </Nav.Item>\n                                </Nav>\n                            </Card.Header>\n                            <Card.Body>\n                                {renderContent()}\n                            </Card.Body>\n                        </Card>\n                    </Col>\n                </Row>\n\n            <h2 className=\"mt-5 mb-4\">{t('my_account')}</h2>\n            <Row>\n                <Col md={6}>\n                    <Card>\n                        <Card.Header>{t('personal_info')}</Card.Header>\n                        <ListGroup variant=\"flush\">\n                            <ListGroup.Item>\n                                <Link to=\"/my/kyc\">{t('kyc_verification')}</Link>\n                            </ListGroup.Item>\n                            <ListGroup.Item>\n                                <Link to=\"/my/change-login-pass\">{t('change_login_password')}</Link>\n                            </ListGroup.Item>\n                            <ListGroup.Item>\n                                <Link to=\"/my/change-withdraw-pass\">{t('change_withdraw_password')}</Link>\n                            </ListGroup.Item>\n                        </ListGroup>\n                    </Card>\n                </Col>\n                <Col md={6}>\n                    <Card>\n                        <Card.Header>{t('my_recommendations')}</Card.Header>\n                        <ListGroup variant=\"flush\">\n                            <ListGroup.Item>\n                                <Link to=\"/my/recommend\">{t('view_my_recommendations')}</Link>\n                            </ListGroup.Item>\n                        </ListGroup>\n                    </Card>\n                </Col>\n            </Row>\n        </Container>\n    );\n};\n\nexport default MyAccountPage;\n"], "mappings": "AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,SAAS,CAAEC,KAAK,CAAEC,MAAM,CAAEC,GAAG,KAAQ,iBAAiB,CAC1F,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,sBAAsB,CAClD,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAEC,CAAE,CAAC,CAAGR,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACS,MAAM,CAAEC,SAAS,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACqB,OAAO,CAAEC,UAAU,CAAC,CAAGtB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACuB,SAAS,CAAEC,YAAY,CAAC,CAAGxB,QAAQ,CAAC,UAAU,CAAC,CAAE;AAExDC,SAAS,CAAC,IAAM,CACZ,KAAM,CAAAwB,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC5B,KAAM,CAAAC,QAAQ,CAAGf,WAAW,CAAC,CAAC,CAC9B,GAAI,CAACe,QAAQ,CAAE,OAEfJ,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEK,IAAI,CAAE,CAAEC,IAAK,CAAE,CAAC,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAExD,GAAI,CAACF,IAAI,CAAE,CACPN,UAAU,CAAC,KAAK,CAAC,CACjB,OAAQ;AACZ,CAEA,KAAM,CAAEK,IAAI,CAAEI,KAAM,CAAC,CAAG,KAAM,CAAAL,QAAQ,CACjCM,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,CAAEN,IAAI,CAACO,EAAE,CAAC,CAE3B,GAAIJ,KAAK,CAAE,CACPK,OAAO,CAACL,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAClD,CAAC,IAAM,CACHX,SAAS,CAACO,IAAI,CAAC,CACnB,CACAL,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,CAEDG,WAAW,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIJ,OAAO,CAAE,CACT,mBAAOP,IAAA,QAAAuB,QAAA,CAAMnB,CAAC,CAAC,gBAAgB,CAAC,CAAM,CAAC,CAC3C,CAEA,KAAM,CAAAoB,aAAa,CAAGA,CAAA,GAAM,CACxB,OAAQf,SAAS,EACb,IAAK,UAAU,CACX,mBACIP,KAAA,CAACT,KAAK,EAACgC,OAAO,MAACC,QAAQ,MAACC,KAAK,MAACC,UAAU,MAAAL,QAAA,eACpCvB,IAAA,UAAAuB,QAAA,cACIrB,KAAA,OAAAqB,QAAA,eACIvB,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,UAAU,CAAC,CAAK,CAAC,cACxBJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cACjCJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,gBAAgB,CAAC,CAAK,CAAC,cAC9BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,eAAe,CAAC,CAAK,CAAC,cAC7BJ,IAAA,OAAAuB,QAAA,CAAKnB,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,EACzB,CAAC,CACF,CAAC,cACRJ,IAAA,UAAAuB,QAAA,CACKlB,MAAM,CAACwB,MAAM,GAAK,CAAC,cAChB7B,IAAA,OAAAuB,QAAA,cACIvB,IAAA,OAAI8B,OAAO,CAAC,GAAG,CAACC,SAAS,CAAC,aAAa,CAAAR,QAAA,CAAEnB,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,CAC7D,CAAC,CAELC,MAAM,CAAC2B,GAAG,CAACC,KAAK,eACZ/B,KAAA,OAAAqB,QAAA,eACIvB,IAAA,OAAAuB,QAAA,CAAKU,KAAK,CAACC,aAAa,CAAK,CAAC,cAC9BlC,IAAA,OAAAuB,QAAA,CAAKU,KAAK,CAACE,iBAAiB,CAAK,CAAC,cAClCnC,IAAA,OAAAuB,QAAA,CAAKU,KAAK,CAACG,cAAc,CAAK,CAAC,cAC/BpC,IAAA,OAAAuB,QAAA,CAAKU,KAAK,CAACI,aAAa,CAAK,CAAC,cAC9BrC,IAAA,OAAAuB,QAAA,CAAKU,KAAK,CAACK,eAAe,CAAK,CAAC,GAL3BL,KAAK,CAACC,aAMX,CACP,CACJ,CACE,CAAC,EACL,CAAC,CAEhB,IAAK,SAAS,CACV,mBAAOlC,IAAA,QAAAuB,QAAA,CAAMnB,CAAC,CAAC,qBAAqB,CAAC,CAAM,CAAC,CAAE;AAClD,IAAK,UAAU,CACX,mBAAOJ,IAAA,QAAAuB,QAAA,CAAMnB,CAAC,CAAC,sBAAsB,CAAC,CAAM,CAAC,CAAE;AACnD,IAAK,UAAU,CACX,mBAAOJ,IAAA,QAAAuB,QAAA,CAAMnB,CAAC,CAAC,sBAAsB,CAAC,CAAM,CAAC,CAAE;AACnD,QACI,MAAO,KAAI,CACnB,CACJ,CAAC,CAED,mBACIF,KAAA,CAACd,SAAS,EAAAmC,QAAA,eACNvB,IAAA,OAAI+B,SAAS,CAAC,MAAM,CAAAR,QAAA,CAAEnB,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACtCJ,IAAA,CAACX,GAAG,EAAAkC,QAAA,cACAvB,IAAA,CAACV,GAAG,EAAAiC,QAAA,cACArB,KAAA,CAACX,IAAI,EAAAgC,QAAA,eACDvB,IAAA,CAACT,IAAI,CAACgD,MAAM,EAAAhB,QAAA,cACRrB,KAAA,CAACP,GAAG,EAAC6C,OAAO,CAAC,MAAM,CAACC,gBAAgB,CAAC,UAAU,CAACC,QAAQ,CAAGC,WAAW,EAAKjC,YAAY,CAACiC,WAAW,CAAE,CAAApB,QAAA,eACjGvB,IAAA,CAACL,GAAG,CAACiD,IAAI,EAAArB,QAAA,cACLvB,IAAA,CAACL,GAAG,CAACG,IAAI,EAAC+C,QAAQ,CAAC,UAAU,CAAAtB,QAAA,CAAEnB,CAAC,CAAC,UAAU,CAAC,CAAW,CAAC,CAClD,CAAC,cACXJ,IAAA,CAACL,GAAG,CAACiD,IAAI,EAAArB,QAAA,cACLvB,IAAA,CAACL,GAAG,CAACG,IAAI,EAAC+C,QAAQ,CAAC,SAAS,CAAAtB,QAAA,CAAEnB,CAAC,CAAC,SAAS,CAAC,CAAW,CAAC,CAChD,CAAC,cACXJ,IAAA,CAACL,GAAG,CAACiD,IAAI,EAAArB,QAAA,cACLvB,IAAA,CAACL,GAAG,CAACG,IAAI,EAAC+C,QAAQ,CAAC,UAAU,CAAAtB,QAAA,CAAEnB,CAAC,CAAC,UAAU,CAAC,CAAW,CAAC,CAClD,CAAC,cACXJ,IAAA,CAACL,GAAG,CAACiD,IAAI,EAAArB,QAAA,cACLvB,IAAA,CAACL,GAAG,CAACG,IAAI,EAAC+C,QAAQ,CAAC,UAAU,CAAAtB,QAAA,CAAEnB,CAAC,CAAC,UAAU,CAAC,CAAW,CAAC,CAClD,CAAC,EACV,CAAC,CACG,CAAC,cACdJ,IAAA,CAACT,IAAI,CAACuD,IAAI,EAAAvB,QAAA,CACLC,aAAa,CAAC,CAAC,CACT,CAAC,EACV,CAAC,CACN,CAAC,CACL,CAAC,cAEVxB,IAAA,OAAI+B,SAAS,CAAC,WAAW,CAAAR,QAAA,CAAEnB,CAAC,CAAC,YAAY,CAAC,CAAK,CAAC,cAChDF,KAAA,CAACb,GAAG,EAAAkC,QAAA,eACAvB,IAAA,CAACV,GAAG,EAACyD,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACPrB,KAAA,CAACX,IAAI,EAAAgC,QAAA,eACDvB,IAAA,CAACT,IAAI,CAACgD,MAAM,EAAAhB,QAAA,CAAEnB,CAAC,CAAC,eAAe,CAAC,CAAc,CAAC,cAC/CF,KAAA,CAACV,SAAS,EAACgD,OAAO,CAAC,OAAO,CAAAjB,QAAA,eACtBvB,IAAA,CAACR,SAAS,CAACoD,IAAI,EAAArB,QAAA,cACXvB,IAAA,CAACF,IAAI,EAACkD,EAAE,CAAC,SAAS,CAAAzB,QAAA,CAAEnB,CAAC,CAAC,kBAAkB,CAAC,CAAO,CAAC,CACrC,CAAC,cACjBJ,IAAA,CAACR,SAAS,CAACoD,IAAI,EAAArB,QAAA,cACXvB,IAAA,CAACF,IAAI,EAACkD,EAAE,CAAC,uBAAuB,CAAAzB,QAAA,CAAEnB,CAAC,CAAC,uBAAuB,CAAC,CAAO,CAAC,CACxD,CAAC,cACjBJ,IAAA,CAACR,SAAS,CAACoD,IAAI,EAAArB,QAAA,cACXvB,IAAA,CAACF,IAAI,EAACkD,EAAE,CAAC,0BAA0B,CAAAzB,QAAA,CAAEnB,CAAC,CAAC,0BAA0B,CAAC,CAAO,CAAC,CAC9D,CAAC,EACV,CAAC,EACV,CAAC,CACN,CAAC,cACNJ,IAAA,CAACV,GAAG,EAACyD,EAAE,CAAE,CAAE,CAAAxB,QAAA,cACPrB,KAAA,CAACX,IAAI,EAAAgC,QAAA,eACDvB,IAAA,CAACT,IAAI,CAACgD,MAAM,EAAAhB,QAAA,CAAEnB,CAAC,CAAC,oBAAoB,CAAC,CAAc,CAAC,cACpDJ,IAAA,CAACR,SAAS,EAACgD,OAAO,CAAC,OAAO,CAAAjB,QAAA,cACtBvB,IAAA,CAACR,SAAS,CAACoD,IAAI,EAAArB,QAAA,cACXvB,IAAA,CAACF,IAAI,EAACkD,EAAE,CAAC,eAAe,CAAAzB,QAAA,CAAEnB,CAAC,CAAC,yBAAyB,CAAC,CAAO,CAAC,CAClD,CAAC,CACV,CAAC,EACV,CAAC,CACN,CAAC,EACL,CAAC,EACC,CAAC,CAEpB,CAAC,CAED,cAAe,CAAAD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}