"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[592],{592:(e,s,r)=>{r.r(s),r.d(s,{default:()=>_});var a=r(5043),t=r(4196),n=r(3519),c=r(1072),d=r(8602),i=r(8628),l=r(4737),o=r(8139),h=r.n(o),x=(r(6440),r(1969)),f=r(927),m=r(7852),j=r(6618),u=r(2644),v=r(5901),b=r(579);const p=a.forwardRef((e,s)=>{let{bsPrefix:r,active:a,disabled:t,eventKey:n,className:c,variant:d,action:i,as:l,...o}=e;r=(0,m.oU)(r,"list-group-item");const[x,f]=(0,u.M)({key:(0,v.u)(n,o.href),active:a,...o}),p=(0,j.A)(e=>{if(t)return e.preventDefault(),void e.stopPropagation();x.onClick(e)});t&&void 0===o.tabIndex&&(o.tabIndex=-1,o["aria-disabled"]=!0);const N=l||(i?o.href?"a":"button":"div");return(0,b.jsx)(N,{ref:s,...o,...x,onClick:p,className:h()(c,r,f.isActive&&"active",t&&"disabled",d&&`${r}-${d}`,i&&`${r}-action`)})});p.displayName="ListGroupItem";const N=p,y=a.forwardRef((e,s)=>{const{className:r,bsPrefix:a,variant:t,horizontal:n,numbered:c,as:d="div",...i}=(0,x.Zw)(e,{activeKey:"onSelect"}),l=(0,m.oU)(a,"list-group");let o;return n&&(o=!0===n?"horizontal":`horizontal-${n}`),(0,b.jsx)(f.A,{ref:s,...i,as:d,className:h()(r,l,t&&`${l}-${t}`,o&&`${l}-${o}`,c&&`${l}-numbered`)})});y.displayName="ListGroup";const w=Object.assign(y,{Item:N});var $=r(4117),g=r(4312),A=r(1283);const _=()=>{const{t:e}=(0,$.Bd)(),[s,r]=(0,a.useState)([]),[o,h]=(0,a.useState)(!0),[x,f]=(0,a.useState)("overview");if((0,a.useEffect)(()=>{(async()=>{const e=(0,g.b)();if(!e)return;h(!0);const{data:{user:s}}=await e.auth.getUser();if(!s)return void h(!1);const{data:a,error:t}=await e.from("user_assets").select("*").eq("user_id",s.id);t?console.error("Error fetching assets:",t):r(a),h(!1)})()},[]),o)return(0,b.jsx)("div",{children:e("loading_wallet")});return(0,b.jsxs)(n.A,{children:[(0,b.jsx)("h2",{className:"mb-4",children:e("my_wallet")}),(0,b.jsx)(c.A,{children:(0,b.jsx)(d.A,{children:(0,b.jsxs)(i.A,{children:[(0,b.jsx)(i.A.Header,{children:(0,b.jsxs)(l.A,{variant:"tabs",defaultActiveKey:"overview",onSelect:e=>f(e),children:[(0,b.jsx)(l.A.Item,{children:(0,b.jsx)(l.A.Link,{eventKey:"overview",children:e("overview")})}),(0,b.jsx)(l.A.Item,{children:(0,b.jsx)(l.A.Link,{eventKey:"deposit",children:e("deposit")})}),(0,b.jsx)(l.A.Item,{children:(0,b.jsx)(l.A.Link,{eventKey:"withdraw",children:e("withdraw")})}),(0,b.jsx)(l.A.Item,{children:(0,b.jsx)(l.A.Link,{eventKey:"exchange",children:e("exchange")})})]})}),(0,b.jsx)(i.A.Body,{children:(()=>{switch(x){case"overview":return(0,b.jsxs)(t.A,{striped:!0,bordered:!0,hover:!0,responsive:!0,children:[(0,b.jsx)("thead",{children:(0,b.jsxs)("tr",{children:[(0,b.jsx)("th",{children:e("currency")}),(0,b.jsx)("th",{children:e("available_balance")}),(0,b.jsx)("th",{children:e("locked_balance")}),(0,b.jsx)("th",{children:e("total_balance")}),(0,b.jsx)("th",{children:e("withdrawn")})]})}),(0,b.jsx)("tbody",{children:0===s.length?(0,b.jsx)("tr",{children:(0,b.jsx)("td",{colSpan:"5",className:"text-center",children:e("no_assets")})}):s.map(e=>(0,b.jsxs)("tr",{children:[(0,b.jsx)("td",{children:e.currency_code}),(0,b.jsx)("td",{children:e.available_balance}),(0,b.jsx)("td",{children:e.balance_locked}),(0,b.jsx)("td",{children:e.balance_total}),(0,b.jsx)("td",{children:e.withdrawn_total})]},e.currency_code))})]});case"deposit":return(0,b.jsx)("div",{children:e("deposit_coming_soon")});case"withdraw":return(0,b.jsx)("div",{children:e("withdraw_coming_soon")});case"exchange":return(0,b.jsx)("div",{children:e("exchange_coming_soon")});default:return null}})()})]})})}),(0,b.jsx)("h2",{className:"mt-5 mb-4",children:e("my_account")}),(0,b.jsxs)(c.A,{children:[(0,b.jsx)(d.A,{md:6,children:(0,b.jsxs)(i.A,{children:[(0,b.jsx)(i.A.Header,{children:e("personal_info")}),(0,b.jsxs)(w,{variant:"flush",children:[(0,b.jsx)(w.Item,{children:(0,b.jsx)(A.N_,{to:"/my/kyc",children:e("kyc_verification")})}),(0,b.jsx)(w.Item,{children:(0,b.jsx)(A.N_,{to:"/my/change-login-pass",children:e("change_login_password")})}),(0,b.jsx)(w.Item,{children:(0,b.jsx)(A.N_,{to:"/my/change-withdraw-pass",children:e("change_withdraw_password")})})]})]})}),(0,b.jsx)(d.A,{md:6,children:(0,b.jsxs)(i.A,{children:[(0,b.jsx)(i.A.Header,{children:e("my_recommendations")}),(0,b.jsx)(w,{variant:"flush",children:(0,b.jsx)(w.Item,{children:(0,b.jsx)(A.N_,{to:"/my/recommend",children:e("view_my_recommendations")})})})]})})]})]})}},1072:(e,s,r)=>{r.d(s,{A:()=>l});var a=r(8139),t=r.n(a),n=r(5043),c=r(7852),d=r(579);const i=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:n="div",...i}=e;const l=(0,c.oU)(r,"row"),o=(0,c.gy)(),h=(0,c.Jm)(),x=`${l}-cols`,f=[];return o.forEach(e=>{const s=i[e];let r;delete i[e],null!=s&&"object"===typeof s?({cols:r}=s):r=s;const a=e!==h?`-${e}`:"";null!=r&&f.push(`${x}${a}-${r}`)}),(0,d.jsx)(n,{ref:s,...i,className:t()(a,l,...f)})});i.displayName="Row";const l=i},4196:(e,s,r)=>{r.d(s,{A:()=>l});var a=r(8139),t=r.n(a),n=r(5043),c=r(7852),d=r(579);const i=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,striped:n,bordered:i,borderless:l,hover:o,size:h,variant:x,responsive:f,...m}=e;const j=(0,c.oU)(r,"table"),u=t()(a,j,x&&`${j}-${x}`,h&&`${j}-${h}`,n&&`${j}-${"string"===typeof n?`striped-${n}`:"striped"}`,i&&`${j}-bordered`,l&&`${j}-borderless`,o&&`${j}-hover`),v=(0,d.jsx)("table",{...m,className:u,ref:s});if(f){let e=`${j}-responsive`;return"string"===typeof f&&(e=`${e}-${f}`),(0,d.jsx)("div",{className:e,children:v})}return v});i.displayName="Table";const l=i},8602:(e,s,r)=>{r.d(s,{A:()=>l});var a=r(8139),t=r.n(a),n=r(5043),c=r(7852),d=r(579);const i=n.forwardRef((e,s)=>{const[{className:r,...a},{as:n="div",bsPrefix:i,spans:l}]=function(e){let{as:s,bsPrefix:r,className:a,...n}=e;r=(0,c.oU)(r,"col");const d=(0,c.gy)(),i=(0,c.Jm)(),l=[],o=[];return d.forEach(e=>{const s=n[e];let a,t,c;delete n[e],"object"===typeof s&&null!=s?({span:a,offset:t,order:c}=s):a=s;const d=e!==i?`-${e}`:"";a&&l.push(!0===a?`${r}${d}`:`${r}${d}-${a}`),null!=c&&o.push(`order${d}-${c}`),null!=t&&o.push(`offset${d}-${t}`)}),[{...n,className:t()(a,...l,...o)},{as:s,bsPrefix:r,spans:l}]}(e);return(0,d.jsx)(n,{...a,ref:s,className:t()(r,!l.length&&i)})});i.displayName="Col";const l=i},8628:(e,s,r)=>{r.d(s,{A:()=>U});var a=r(8139),t=r.n(a),n=r(5043),c=r(7852),d=r(579);const i=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...i}=e;return a=(0,c.oU)(a,"card-body"),(0,d.jsx)(n,{ref:s,className:t()(r,a),...i})});i.displayName="CardBody";const l=i,o=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...i}=e;return a=(0,c.oU)(a,"card-footer"),(0,d.jsx)(n,{ref:s,className:t()(r,a),...i})});o.displayName="CardFooter";const h=o;var x=r(1778);const f=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,as:i="div",...l}=e;const o=(0,c.oU)(r,"card-header"),h=(0,n.useMemo)(()=>({cardHeaderBsPrefix:o}),[o]);return(0,d.jsx)(x.A.Provider,{value:h,children:(0,d.jsx)(i,{ref:s,...l,className:t()(a,o)})})});f.displayName="CardHeader";const m=f,j=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,variant:n,as:i="img",...l}=e;const o=(0,c.oU)(r,"card-img");return(0,d.jsx)(i,{ref:s,className:t()(n?`${o}-${n}`:o,a),...l})});j.displayName="CardImg";const u=j,v=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="div",...i}=e;return a=(0,c.oU)(a,"card-img-overlay"),(0,d.jsx)(n,{ref:s,className:t()(r,a),...i})});v.displayName="CardImgOverlay";const b=v,p=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="a",...i}=e;return a=(0,c.oU)(a,"card-link"),(0,d.jsx)(n,{ref:s,className:t()(r,a),...i})});p.displayName="CardLink";const N=p;var y=r(4488);const w=(0,y.A)("h6"),$=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n=w,...i}=e;return a=(0,c.oU)(a,"card-subtitle"),(0,d.jsx)(n,{ref:s,className:t()(r,a),...i})});$.displayName="CardSubtitle";const g=$,A=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n="p",...i}=e;return a=(0,c.oU)(a,"card-text"),(0,d.jsx)(n,{ref:s,className:t()(r,a),...i})});A.displayName="CardText";const _=A,P=(0,y.A)("h5"),k=n.forwardRef((e,s)=>{let{className:r,bsPrefix:a,as:n=P,...i}=e;return a=(0,c.oU)(a,"card-title"),(0,d.jsx)(n,{ref:s,className:t()(r,a),...i})});k.displayName="CardTitle";const I=k,R=n.forwardRef((e,s)=>{let{bsPrefix:r,className:a,bg:n,text:i,border:o,body:h=!1,children:x,as:f="div",...m}=e;const j=(0,c.oU)(r,"card");return(0,d.jsx)(f,{ref:s,...m,className:t()(a,j,n&&`bg-${n}`,i&&`text-${i}`,o&&`border-${o}`),children:h?(0,d.jsx)(l,{children:x}):x})});R.displayName="Card";const U=Object.assign(R,{Img:u,Title:I,Subtitle:g,Body:l,Link:N,Text:_,Header:m,Footer:h,ImgOverlay:b})}}]);
//# sourceMappingURL=592.56ca1752.chunk.js.map